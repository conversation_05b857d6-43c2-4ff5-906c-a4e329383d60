$(document).ready(() => {
    const { loadExternalSource } = prestashop.themeSelectors.alysum.utils;

    const addPSEventListeneres = () => {
        prestashop.on('clickQuickView', () => {
            initVerticalCarousel();
        });

        prestashop.on('updatedProduct', () => {
            queueMicrotask(() => {
                initZoomper();
                updateReferenceCode();
                initVerticalCarousel();
            });
        });
    };

    const updateReferenceCode = () => {
        const source = document.querySelector('.product-reference span');
        const target = document.querySelector('.product-reference-top span');
        if (source?.textContent) {
            target.textContent = source.textContent;
        }
    };

    const initVerticalCarousel = async () => {
        try {
            await loadExternalSource(
                `${prestashop.urls.js_url}libs/verticalCarousel.min.js`,
                'script',
                'pk-vertical-carousel'
            );
        } catch (error) {
            return console.warn('Unable to load vertical carousel');
        }

        const carouselRoot = $('.js-vCarousel');
        const showItems = 4;
        const itemsNumber = carouselRoot.find(prestashop.themeSelectors.product.thumbContainer).length;

        if (itemsNumber <= showItems) {
            return;
        }

        const config = {
            currentItem: 1,
            showItems,
        };

        carouselRoot.imagesLoaded(
            queueMicrotask(() =>
                carouselRoot.verticalCarousel(config).addClass('scroll').removeClass('hidden')
            )
        );
    };

    const initZoomper = async () => {
        const disableMobileInnnerZoom = false;

        if (disableMobileInnnerZoom && prestashop.responsive.mobile && pktheme.pp_innnerzoom) {
            return false;
        }

        try {
            await loadExternalSource(
                `${prestashop.urls.js_url}libs/jquery.zoom.min.js`,
                'script',
                'pk-jqueryzoom'
            );
        } catch (error) {
            return console.warn('Unable to load image zoom');
        }

        const initZoomElement = () => {
            queueMicrotask(() => {
                const url = document.querySelector('.js-qv-product-cover')?.getAttribute('src');
                url && $('.prod-image-zoom').trigger('zoom.destroy').zoom({ url });
            });
        };

        // init zoom on load
        initZoomElement();

        // init zoom on click
        document
            .querySelectorAll('.js-qv-product-images img')
            .forEach((thumb) => thumb.addEventListener('click', initZoomElement));
    };

    initZoomper();
    initVerticalCarousel();
    addPSEventListeneres();
});
