const pkProductAttribureIDChanger = () => {
    const attributes = document.querySelectorAll('.pl_attr');
    attributes?.forEach((attr) =>
        attr.addEventListener('click', ({ target }) => {
            const thumbnailContainer = target.closest(prestashop.themeSelectors.listing.product);
            const productAttributeId = thumbnailContainer.querySelector('[name="id_product_attribute"]');
            productAttributeId.value = target.dataset.id_attribute;
        })
    );

    prestashop.on('updateProductList', () => pkProductAttribureIDChanger());
};

export default pkProductAttribureIDChanger;
