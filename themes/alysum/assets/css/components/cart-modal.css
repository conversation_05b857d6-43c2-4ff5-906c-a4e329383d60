.blockcart-modal {
    .media-list li:not(:last-child) {
        border-bottom: 1px dashed #ccc;
        margin-bottom: 3px;
    }
    .product-image {
        width: 100%;
    }
    .product-card {
        width: 65%;
        gap: 30px;
        border-right: 1px solid #eee;
        picture {
            width: 40%;
        }
    }
    .product-info {
        padding-right: 25px;
        .product-name {
            color: #414141;
            font-size: 1.5rem;
            line-height: 120%;
            margin: 0 0 20px;
        }
        & span {
            display: block;
        }
        .price {
            font-size: 18px;
            margin-bottom: 20px;
        }
    }
    .product-properties {
        i {
            color: #414141;
        }
    }
    .cart-content {
        gap: 20px;
        width: 35%;
        p strong {
            font-weight: 400;
            font-style: normal;
            color: #888;
        }
    }
    .cart-actions {
        gap: 10px;
    }
}
@media (max-width: 800px) {
    .pk-modal {
        top: 5%;
        transform: translate(-50%, 0);
        .modal-body {
            flex-direction: column;
            .product-card {
                border: none;
                width: 100%;
            }
            .cart-content {
                width: 100%;
            }
        }
    }
}
@media (max-width: 400px) {
    .blockcart-modal {
        .product-card {
            flex-direction: column;
            picture {
                width: 100%;
            }
        }
    }
}
