<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <title> Payment </title>
    <!--[if !mso]><!-- -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        #outlook a {
            padding: 0;
        }

        .ReadMsgBody {
            width: 100%;
        }

        .ExternalClass {
            width: 100%;
        }

        .ExternalClass * {
            line-height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }
    </style>
    <!--[if !mso]><!-->
    <style type="text/css">
        @media only screen and (max-width: 480px) {
            @-ms-viewport {
                width: 320px;
            }

            @viewport {
                width: 320px;
            }
        }
    </style>
    <!--<![endif]-->
    <!--[if mso]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <!--[if lte mso 11]>
    <style type="text/css">
        .outlook-group-fix {
            width: 100% !important;
        }
    </style>
    <![endif]-->
    <style type="text/css">
    </style>
    <style type="text/css">
        .shadow {
            box-shadow: 0 20px 30px 0 rgba(0, 0, 0, 0.1);
        }

        .label {
            font-weight: 700;
        }

        .warning {
            font-weight: 700;
            font-size: 16px;
        }

        a {
            color: #25B9D7;
            text-decoration: underline;
            font-weight: 600;
        }

        a.light {
            font-weight: 400;
        }

        span.strong {
            font-weight: 600;
        }

        @media only screen and (max-width: 480px) {

            body,
            .no-bg {
                background-color: #fff !important;
            }

            .left p {
                text-align: left;
                display: inline-block
            }
        }
    </style>

    <style type="text/css">
        #outlook a {
            padding: 0;
        }

        .ReadMsgBody {
            width: 100%;
        }

        .ExternalClass {
            width: 100%;
        }

        .ExternalClass * {
            line-height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }
    </style>
    <style type="text/css">
        @media only screen and (max-width: 480px) {
            @-ms-viewport {
                width: 320px;
            }

            @viewport {
                width: 320px;
            }
        }
    </style>
    <style type="text/css">
        @import url(https://fonts.googleapis.com/css?family=Open+Sans:300,400,500,700);
        @import url(https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i);
    </style>
    <style type="text/css">
        @media only screen and (min-width: 480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }

            .mj-column-px-25 {
                width: 25px !important;
                max-width: 25px;
            }
        }
    </style>
    <style type="text/css">
    </style>
    <style type="text/css">
        .shadow {
            box-shadow: 0 20px 30px 0 rgba(0, 0, 0, 0.1);
        }

        .label {
            font-weight: 700;
        }

        .warning {
            font-weight: 700;
            font-size: 16px;
        }

        a {
            color: #25B9D7;
            text-decoration: underline;
            font-weight: 600;
        }

        a.light {
            font-weight: 400;
        }

        span.strong {
            font-weight: 600;
        }

        @media only screen and (max-width: 480px) {

            body,
            .no-bg {
                background-color: #fff !important;
            }

            .left p {
                text-align: left;
                display: inline-block
            }
        }
    </style>

</head>

<body style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #eeeeee;"
      bgcolor="#eeeeee">
<div style="background-color: #eeeeee;" bgcolor="#eeeeee"><!-- [if mso | IE]>

    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td height="40" style="vertical-align:top;height:40px;">

    <![endif]-->
<div style="height: 40px;" height="40">&nbsp;</div>
<!-- [if mso | IE]>

    </td></tr></table>


    <table
            align="center" border="0" cellpadding="0" cellspacing="0" class="shadow-outlook wrapper-container-outlook"
            style="width:604px;" width="604"
    >
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
    <![endif]-->
<div class="shadow wrapper-container" style="box-shadow: 0 20px 30px 0 rgba(0, 0, 0, 0.1); background: #ffffff; background-color: #ffffff; margin: 0px auto; border-radius: 4px; max-width: 604px;" bgcolor="#ffffff">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; background-color: #ffffff; width: 100%; border-radius: 4px;" bgcolor="#ffffff" width="100%">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 0 0 30px; text-align: center; vertical-align: top;" align="center"><!-- [if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                    <![endif]--> <!-- [if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                    <![endif]--> <!-- LOGO BEGINING --> <!-- [if mso | IE]>
                    <tr>
                        <td
                                class="" width="604px"
                        >

                            <table
                                    align="center" border="0" cellpadding="0" cellspacing="0" class=""
                                    style="width:604px;" width="604"
                            >
                                <tr>
                                    <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                    <![endif]-->
<div style="margin: 0px auto; max-width: 604px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 45px 25px; text-align: center; vertical-align: top;" align="center"><!-- [if mso | IE]>
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                                        <tr>

                                            <td
                                                    class="" style="vertical-align:top;width:554px;"
                                            >
                                    <![endif]-->
<div class="mj-column-per-100 outlook-group-fix" style="font-size: 13px; text-align: left; direction: ltr; display: inline-block; vertical-align: top; width: 100%;" align="left" width="100%">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; vertical-align: top;" width="100%">
<tbody>
<tr>
<td align="left" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0px; padding: 10px 25px; word-break: break-word;">
<table border="0" cellpadding="0" cellspacing="0px" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 150px;" width="150"><a href="{shop_url}" target="_blank" style="color: #25b9d7; text-decoration: underline; font-weight: 600;"> <img height="auto" src="{shop_logo}" style="line-height: 100%; -ms-interpolation-mode: bicubic; border: 0; display: block; outline: none; text-decoration: none; height: auto; width: 100%; font-size: 13px;" width="100%" border="0" /> </a></td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                                    </td>

                                    </tr>

                                    </table>
                                    <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                    </td>
                    </tr>
                    </table>

                    </td>
                    </tr>
                    <![endif]--> <!-- LOGO ENDING --> <!-- [if mso | IE]>
                    </table>
                    <![endif]--> <!-- [if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                        <tr>
                            <td
                                    class="" width="604px"
                            >

                                <table
                                        align="center" border="0" cellpadding="0" cellspacing="0" class=""
                                        style="width:604px;" width="604"
                                >
                                    <tr>
                                        <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                    <![endif]-->
<div style="margin: 0px auto; max-width: 604px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 0 25px; text-align: center; vertical-align: top;" align="center"><!-- [if mso | IE]>
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                                        <tr>

                                            <td
                                                    class="" style="vertical-align:top;width:554px;"
                                            >
                                    <![endif]-->
<div class="mj-column-per-100 outlook-group-fix" style="font-size: 13px; text-align: left; direction: ltr; display: inline-block; vertical-align: top; width: 100%;" align="left" width="100%">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; vertical-align: top; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%"><!-- TITLE BEGINING -->
<tbody>
<tr>
<td align="left" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0px; padding: 10px 25px; padding-top: 0; padding-bottom: 20px; word-break: break-word;">
<div style="font-family: Open sans, arial, sans-serif; font-size: 20px; font-weight: 600; line-height: 25px; text-align: left; color: #363a41;" align="left">Hi firstName lastName,</div>
</td>
</tr>
<!-- TITLE ENDING --> <!-- BORDER BEGINING --></tbody>
</table>
</td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                                    </td>

                                    </tr>

                                    </table>
                                    <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                    </td>
                    </tr>
                    </table>

                    </td>
                    </tr>

                    <tr>
                        <td
                                class="" width="604px"
                        >

                            <table
                                    align="center" border="0" cellpadding="0" cellspacing="0" class=""
                                    style="width:604px;" width="604"
                            >
                                <tr>
                                    <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                    <![endif]-->
<div style="margin: 0px auto; max-width: 604px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 0 50px 40px; text-align: left; vertical-align: top;" align="left"><!-- [if mso | IE]>
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                                        <tr>

                                            <td
                                                    class="" style="vertical-align:top;width:25px;"
                                            >
                                    <![endif]-->
<div class="mj-column-px-25 outlook-group-fix" style="font-size: 13px; text-align: left; direction: ltr; display: inline-block; vertical-align: top; width: 100%;" align="left" width="100%">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; vertical-align: top;" width="100%">
<tbody>
<tr>
<td class="left" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0px; padding: 10px 25px; padding-top: 0; padding-right: 0; padding-left: 0; word-break: break-word;">
<p style="display: block; border-top: solid 3px #505050; font-size: 1; margin: 0px auto; width: 25px;" width="25"></p>
<!-- [if mso | IE]>
                                                                                                        <table
                                                                                                                align="center" border="0" cellpadding="0" cellspacing="0"
                                                                                                                style="border-top:solid 3px #505050;font-size:1;margin:0px auto;width:25px;"
                                                                                                                role="presentation" width="25px"
                                                                                                        >
                                                                                                            <tr>
                                                                                                                <td style="height:0;line-height:0;">
                                                                                                                    &nbsp;
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        </table>
                                                                                                        <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                                    </td>

                                    </tr>

                                    </table>
                                    <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                    </td>
                    </tr>
                    </table>

                    </td>
                    </tr>
                    <![endif]--> <!-- BORDER ENDING --> <!-- SUBTITLE BEGINING --> <!-- [if mso | IE]>
                    <tr>
                        <td
                                class="" width="604px"
                        >

                            <table
                                    align="center" border="0" cellpadding="0" cellspacing="0" class=""
                                    style="width:604px;" width="604"
                            >
                                <tr>
                                    <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                    <![endif]-->
<div style="margin: 0px auto; max-width: 604px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 0 25px 0; text-align: center; vertical-align: top;" align="center"><!-- [if mso | IE]>
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                                        <tr>

                                            <td
                                                    class="" style="vertical-align:top;width:554px;"
                                            >
                                    <![endif]-->
<div class="mj-column-per-100 outlook-group-fix" style="font-size: 13px; text-align: left; direction: ltr; display: inline-block; vertical-align: top; width: 100%;" align="left" width="100%">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; vertical-align: top; padding: 0;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="548" height="90">
<tbody>
<tr>
<td align="left" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0px; padding: 10px 25px; padding-top: 0px; padding-bottom: 0px; word-break: break-word;">
<div style="font-family: Open sans, arial, sans-serif; font-size: 16px; font-weight: 600; line-height: 25px; text-align: left; color: #363a41;" align="left"><span>We are emailing you because we have retried to process the payment and failed. Your subscription has been cancelled.</span></div>
</td>
</tr>
<tr>
<td style="padding: 10px 25px;"></td>
</tr>
<tr>
<td align="left" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-size: 0px; padding: 10px 25px; padding-top: 0px; padding-bottom: 0px; word-break: break-word;"></td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                                    </td>

                                    </tr>

                                    </table>
                                    <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                    </td>
                    </tr>
                    </table>

                    </td>
                    </tr>
                    <![endif]--> <!-- SUBTITLE ENDING --> <!-- BOX BEGINING --> <!-- [if mso | IE]>
                    <tr>
                        <td
                                class="" width="604px"
                        >

                            <table
                                    align="center" border="0" cellpadding="0" cellspacing="0" class=""
                                    style="width:604px;" width="604"
                            >
                                <tr>
                                    <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                    <![endif]-->
<div style="margin: 0px auto; max-width: 604px;">t
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; width: 600px;" width="100%" height="85">
<tbody>
<tr>
<td style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; direction: ltr; font-size: 0px; padding: 15px 50px 40px; text-align: center; vertical-align: top;" align="center"><!-- [if mso | IE]>
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                                        <tr>

                                            <td
                                                    class="" style="vertical-align:top;width:504px;"
                                            >
                                    <![endif]--></td>
</tr>
<tr><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">Reference</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">Product</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">Unit price(tax excl.)</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">Quantity</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">Total price</th></tr>
<tr><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">subscription_reference</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">product_name</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">unit_price</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">quantity</th><th bgcolor="#FDFDFD" style="font-family: Open sans, Arial, sans-serif; font-size: 12px; background-color: #fdfdfd; color: #353943; font-weight: 600; padding: 10px 5px; border: 1px solid #DFDFDF;">total_price</th></tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
                    </td>

                    </tr>

                    </table>
                    <![endif]--></td>
</tr>
</tbody>
</table>
</div>
<!-- [if mso | IE]>
    </td>
    </tr>
    </table>

    </td>
    </tr>
    <![endif]--> <!-- BOX ENDING --> <!-- FIRST TEXT BEGINING --> <!--[endif]--></div>
<!-- [if mso | IE]>
</td>
</tr>
</table>

</td>
</tr>
<![endif]-->
<p></p>
<!-- FIRST TEXT ENDING -->
<p></p>
<!-- SECOND TEXT BEGINING -->
<p></p>
<!-- [if mso | IE]>
<![endif]-->
<p></p>
<!-- SECOND TEXT ENDING -->
<p></p>
<!-- [if mso | IE]>
</table>
<![endif]-->
<p></p>
<!-- [if mso | IE]>
</table>
<![endif]-->
<p></p>
<!-- [if mso | IE]>
</td>
</tr>
</table>
<![endif]-->
<p></p>
<!-- [if mso | IE]>
<table role="presentation" border="0" cellpadding="0" cellspacing="0">
<![endif]-->
<p></p>
<!-- SHOP NAME BEGINING -->
<p></p>
<!-- [if mso | IE]>
<tr>
    <td
            class="" width="604px"
    >

        <table
                align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:604px;" width="604"
        >
            <tr>
                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
<![endif]-->
<div style="margin: 0px auto; max-width: 604px;"></div>
<!-- [if mso | IE]>
</td>

</tr>

</table>
<![endif]-->
<p></p>
<!-- [if mso | IE]>
</td>
</tr>
</table>

</td>
</tr>
<![endif]-->
<p></p>
<!-- SHOP NAME ENDING -->
<p></p>
<!-- [if mso | IE]>
</table>
<![endif]-->
<p></p>
<p></p>
</body>

</html>
