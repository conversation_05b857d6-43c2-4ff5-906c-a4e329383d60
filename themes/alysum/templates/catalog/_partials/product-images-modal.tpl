{**
 * For the full copyright and license information, please view the LICENSES
 * file that was distributed with Prestashop source code.
 *}

{capture name="body"}
<figure class="relative">
    {include file='_partials/picture.tpl' type='large_default' img=$product.default_image imgClass='js-modal-product-cover product-cover-modal'}
    <figcaption class="image-caption absolute w-100">
        {block name='product_description_short'}
{*            <div id="product-description-short">{$product.description_short nofilter}</div>*}
        {/block}
    </figcaption>
</figure>

{assign var=imagesCount value=$product.images|count}

{if $imagesCount > 1}
<aside id="thumbnails" class="thumbnails js-thumbnails relative">
    {block name='product_images'}
        <ul class="product-images js-modal-product-images">
            {foreach from=$product.images item=image}
                <li class="thumb-container">
                    <img data-image-large-src="{$image.large.url}" class="thumb js-modal-thumb smooth02 db"
                        src="{$image.bySize.product_thumbnail.url}" alt="{$image.legend}" title="{$image.legend}"
                        width="{$image.bySize.product_thumbnail.width}">
                </li>
            {/foreach}
        </ul>
        {if $imagesCount > 4}
        <div class="scroll-box-arrows hidden">
            <i class="up arrow-up js-modal-arrow-up">
                {include file='components/svg-icon.tpl' id='top-arrow-thin' classes='svg-done'}
            </i>
            <i class="down">
                {include file='components/svg-icon.tpl' id='bottom-arrow-thin' classes='svg-done'}
            </i>
        </div>
        {/if}
    {/block}
</aside>
{/if}
{/capture}

{include file="components/modal/modal.tpl" id="product-modal" classes="js-product-images-modal" title=$product.name body=$smarty.capture.body loaded="1"}
