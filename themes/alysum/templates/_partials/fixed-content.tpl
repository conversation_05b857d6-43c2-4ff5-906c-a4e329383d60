{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}

{capture name="bottomRight"}{hook h="displayFixedBottomRight"}{/capture}
{capture name="bottomLeft"}{hook h="displayFixedBottomLeft"}{/capture}

{if $smarty.capture.bottomRight ne ""}
    <div class="pk-fixed-bottom pk-fixed-bottom-right flex-container fixed cp justify-content-left">
        {$smarty.capture.bottomRight nofilter}</div>
{/if}
{if $smarty.capture.bottomLeft ne ""}
    <div class="pk-fixed-bottom pk-fixed-bottom-left flex-container fixed cp justify-content-left">
        {$smarty.capture.bottomLeft nofilter}</div>
{/if}