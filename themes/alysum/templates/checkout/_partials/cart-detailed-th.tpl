<div class="cart-overview js-cart"
    data-refresh-url="{url entity='cart' params=['ajax' => true, 'action' => 'refresh']}">

    {assign var="minimumNet" value=Configuration::get('PS_PURCHASE_MINIMUM')}

    {if isset($th_cart_count) && $th_cart_count > 1}
        <div class="cart-message">
            <span class="cart-message-title">{l s='Information Zur Bestellabwicklung & zum Rechnungssteller:' d='Shop.Theme.Checkout'}</span>
            <span class="cart-message-text">{l s="Unser Marktplatz arbeitet mit unterschiedlichen Rechnungsstellern. Dein Warenkorb enthält Produkte von <b>“%s” Rechnungssteller.</b>" d='Shop.Theme.Checkout' sprintf=[$th_cart_count]}</span>
            <span class="cart-message-text">{l s="Je Rechnungssteller muss ein separater Kassenvorgang abgeschlossen werden. Dass bedeutet für Dich," d='Shop.Theme.Checkout'}</span>
            <span class="cart-message-text">{l s="Du durchläufst den Kassenvorgang je Rechnungssteller inkl. separater Bezahlung und bekommst je Rechnungssteller separate Bestellbestätigungen / Emails / AGB's / Rechnungen usw." d='Shop.Theme.Checkout'}</span>
       </div>
    {/if}

    {if $cart|count > 0}
        {foreach from=$cart item=invoicer key=id_invoicer}
            <div class="cart-invoicer" data-id-invoicer="{$id_invoicer}" {if isset($invoicer.cart_key)}
                data-cart-key="{$invoicer.cart_key}" {/if}>
                {if $id_invoicer == -1}
                    <h2 class="th-invoicer-name">{$invoicer.name}</h2>
                    {foreach from=$invoicer.invoicers key=invoicer_key item=invoicer_data}
                        <div class="th-problematic-invoicer">
                            <h3 class="th-problematic-invoicer-name">
                                {l s='Rechnungssteller' d='Shop.Theme.Checkout'}: {$invoicer_data.name}
                            </h3>
                            {foreach from=$invoicer_data.suppliers key=supplier_key item=supplier_data}
                                <div class="th-problematic-supplier">
                                    <h4 class="th-problematic-supplier-name">{l s='Lieferant' d='Shop.Theme.Checkout'}:
                                        {$supplier_data.name}
                                    </h4>
                                    <div class="th-problematic-supplier-message">
                                        <p> {l s='<u>Achtung:</u> Die von Dir ausgewählten Artikel dieses Lieferanten können nicht in dieses Land versendet werden.' d='Shop.Theme.Checkout'}</p>
                                        <p> {l s='Bitte ändere das Versandland, oder die Artikel in deinem Warenkorb.' d='Shop.Theme.Checkout'}</p>
                                    </div>
                                    <ul class="cart-items">
                                        {foreach from=$supplier_data.products item=product}
                                            <li class="cart-item">
                                                {include file='checkout/_partials/cart-detailed-product-line.tpl' product=$product}
                                            </li>
                                            {if $product.customizations|count >1}
                                                <hr>
                                            {/if}
                                        {/foreach}
                                    </ul>
                                </div>
                            {/foreach}
                        </div>
                    {/foreach}
                {else}
                    <h2 class="th-invoicer-name"><span
                            class="th-invoicer-name-label">{l s='Rechnungssteller' d='Shop.Theme.Checkout'}:</span>{$invoicer.name}
                    </h2>
                    {* Get minimum order amount from PrestaShop configuration *}
                    {assign var="currentNet" value=$invoicer.totals.products.amount}
                    
                    {* Check if current invoicer total is below minimum order amount *}
                    {if $minimumNet > 0 && $currentNet < $minimumNet}
                        {* Calculate minimum gross amount with 19% tax rate *}
                        {assign var="minimumGross" value=$minimumNet * 1.19}
                        
                        <div class="cart-minimum-order alert alert-warning mb-3">
                            <i class="icon-warning"></i>
                            <div class="cart-minimum-order-content">
                                {l s='<b>!! Achtung: - Bitte beachte !!  <u>Unser Mindestbestellwert beträgt %s Euro (inkl. MwSt)</u></b>' d='Shop.Theme.Checkout' sprintf=[$minimumNet|number_format:2:',':'.']}
                            </div>
                            <div class="cart-minimum-order-content">
                                {l s='Der aktuelle Warenkorbwert beträgt lediglich %s Euro (inkl. MWST)' d='Shop.Theme.Checkout' sprintf=[{$currentNet|number_format:2:',':'.'}]}
                            </div>
                            <div class="cart-minimum-order-content">
                                {l s='Bitte füge deinem Warenkorb weitere Artikel hinzu, um diesen Warenwert von mind. %s Euro zu erreichen – Danke!' d='Shop.Theme.Checkout' sprintf=[$minimumNet|number_format:2:',':'.']}
                            </div>
                        </div>
                    {/if}

                    {foreach from=$invoicer.suppliers item=supplier}
                        <div class="cart-supplier">
                            <h3 class="th-supplier-name"><span
                                    class="th-supplier-name-label">{l s='Supplier' d='Shop.Theme.Checkout'}:</span>{$supplier.name}
                            </h3>
                            <ul class="cart-items">
                                {foreach from=$supplier.products item=product}
                                    <li class="cart-item">
                                        {include file='checkout/_partials/cart-detailed-product-line.tpl' product=$product}
                                    </li>
                                    {if $product.customizations|count >1}
                                        <hr>
                                    {/if}
                                {/foreach}
                            </ul>
                            {if $supplier.notes}
                                <div class="cart-supplier-notes">
                                    {$supplier.notes nofilter}
                                </div>
                            {/if}
                            <div class="cart-supplier-totals">
                                {foreach from=$supplier.totals item=total}
                                    <div class="cart-supplier-total">
                                        <div class="cart-supplier-total-label">{$total.label}</div>
                                        <div class="cart-supplier-total-value">{$total.value}</div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    {/foreach}
                    {if $invoicer.notes}
                        <div class="cart-invoicer-notes">
                            {$invoicer.notes nofilter}
                        </div>
                    {/if}
                    <div class="cart-discount-code">
                        <div class="cart-discount-code-title">
                            {l s='Hast Du einen Gutschein-Code?' d='Shop.Theme.Checkout'}
                        </div>
                        <div class="cart-discount-code-subtitle">
                            {l s='(Mehrere Gutscheine? Bitte gebe den Gutschein-Code ein, klicke auf “Rabattcode anwenden” und gebe anschließend den weiteren Gutschein-Code ein)' d='Shop.Theme.Checkout'}
                        </div>
                        <div class="cart-discount-code-list">
                            {foreach from=$invoicer.cart_discount_codes item=discount_code}
                                <div class="cart-discount-code-item" data-id="{$discount_code.id_cart_rule}">
                                    <div class="cart-discount-code-item-label">{$discount_code.name}</div>
                                    <div class="cart-discount-code-item-value">{$discount_code.reduction_formatted}</div>
                                    <button class="cart-discount-code-item-remove">
                                        {include file='components/svg-icon.tpl' id='cross'}
                                    </button>
                                </div>
                            {/foreach}
                        </div>
                        <div class="cart-discount-add">
                            <input type="text" class="cart-discount-code-input"
                                placeholder="{l s='Rabattcode' d='Shop.Theme.Checkout'}">
                            <button
                                class="cart-discount-code-button btn btn-primary">{l s='Rabattcode anwenden' d='Shop.Theme.Checkout'}</button>
                        </div>
                    </div>
                    <div class="cart-invoicer-totals">
                        {foreach from=$invoicer.totals item=total}
                            <div class="cart-invoicer-total">
                                <div class="cart-invoicer-total-label">{$total.label}</div>
                                <div class="cart-invoicer-total-value">{$total.value}</div>
                            </div>
                        {/foreach}
                    </div>
                    <div class="cart-invoicer-payment">
                        <a class="cart-invoicer-payment-button btn btn-primary"
                            href="{$invoicer.pay_url}">{l s='weiter zur Kasse mit Rechnungssteller %s' sprintf=[$invoicer.name_short] d='Shop.Theme.Checkout'}</a>
                    </div>
                {/if}
            </div>
        {/foreach}
    {else}
        <span class="no-items">{l s='There are no more items in your cart' d='Shop.Theme.Checkout'}</span>
    {/if}
</div>