{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *#}
{% set isModuleActive = module.database.active|default('0') %}

<div
  class="module-item module-item-list col-md-12 {% if origin == 'manage' and isModuleActive == '0' %}module-item-list-isNotActive{% endif %}"
  data-id="{{ module.attributes.id }}"
  data-name="{{ module.attributes.displayName }}"
  data-scoring="{{ module.attributes.avgRate }}"
  data-logo="{{ module.attributes.img }}"
  data-author="{{ module.attributes.author }}"
  data-version="{{ module.attributes.version }}"
  data-description="{{ module.attributes.description }}"
  data-tech-name="{{ module.attributes.name }}"
  data-child-categories="{{ module.attributes.categoryName }}"
  data-categories="{{ category }}"
  data-type="{{ module.attributes.productType }}"
  data-price="{{ module.attributes.price.raw }}"
  data-active="{{ isModuleActive }}"
  data-installed="{{ module.database.installed|default('0') }}"
  data-last-access="{{ module.database.last_access_date }}"
>
  <div class="container-fluid">
    <div class="module-item-wrapper-list row">
      <div class="col-lg-1 text-sm-center">
        <div class="module-logo-thumb-list">
          <img src="{{ module.attributes.img }}" class="text-md-center" alt="{{ module.attributes.displayName }}"/>
        </div>
      </div>
      <div class="col-lg-11 row">
        <div class="col-md-10 col-lg-11">
          <h3
            class="text-ellipsis module-name-list"
            data-toggle="pstooltip"
            data-placement="top"
            title="{{ module.attributes.displayName|raw }}"
          >
            {% if module.attributes.displayName %}
              {{ module.attributes.displayName|raw }}

            {% else %}
              {{ module.attributes.name }}
            {% endif %}
          </h3>
        </div>
        <div class="col-md-2">
          <div class="text-ellipsis small-text">
            {% block addon_version %}
              {% if module.attributes.productType == "service" %}
                {{ 'Service by %author%'|trans({'%author%' : '<b>' ~ module.attributes.author ~ '</b>'}, 'Admin.Modules.Feature')|raw }}
              {% else %}
                {{ 'v%version% - by %author%'|trans({ '%version%' : module.attributes.version, '%author%' : '<b>' ~ module.attributes.author ~ '</b>' }, 'Admin.Modules.Feature')|raw }}
              {% endif %}
            {% endblock %}
          </div>
          <div>
            {% if module.attributes.urls.upgrade is defined %}
                <span class="badge badge-success my-1">{{ 'Upgrade available'|trans({}, 'Admin.Modules.Feature') }}</span>
            {% endif %}
          </div>
        </div>
        <div class="col-md-8 col-lg-7">
          {% block addon_description %}
            {{ module.attributes.description|raw }}
            {% if module.attributes.description|length > 0 and module.attributes.description|length < module.attributes.fullDescription|length %}
              ...
            {% endif %}
          {% endblock %}
          {% block addon_additional_description %}
            {% if module.attributes.additional_description is defined %}
              {{ module.attributes.additional_description|raw }}
            {% endif %}
          {% endblock %}
        </div>
        <div class="col-lg-3 text-md-right">
          {% block module_actions %}
            {% if requireBulkActions is defined and requireBulkActions == true %}
              <div class="module-checkbox-bulk-list md-checkbox">
                <label>
                  <input type="checkbox" data-name="{{ module.attributes.displayName }}" data-tech-name="{{module.attributes.name}}" />
                  <i class="md-checkbox-control"></i>
                </label>
              </div>
            {% endif %}
            {% include '@PrestaShop/Admin/Module/Includes/action_menu.html.twig' with { 'module': module } %}
          {% endblock %}
        </div>
        {% include '@PrestaShop/Admin/Module/Includes/modal_confirm.html.twig' with { 'module': module } %}
      </div>
    </div>
  </div>
</div>
