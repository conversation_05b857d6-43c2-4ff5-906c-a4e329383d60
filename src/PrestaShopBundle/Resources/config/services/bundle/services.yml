parameters:
  ps_root_dir: "%kernel.root_dir%/../"
  ps_config_dir: "%ps_root_dir%config"
  translations_dir: "%kernel.root_dir%/../translations"
  themes_translations_dir: "%kernel.cache_dir%/themes"
  modules_dir: "%kernel.root_dir%/../modules"
  themes_dir: "%kernel.root_dir%/../themes"
  translation_catalogues_export_dir: "%kernel.cache_dir%/export"
  translations_catalogue_extract_excluded_dirs: [ 'vendor', 'lib', 'tests' ]

services:
  _defaults:
    public: true

  # Interfaced services to decorate
  prestashop.core.admin.data_provider.product_interface:
    class: PrestaShopBundle\Service\DataProvider\Admin\ProductInterface
  prestashop.core.admin.data_updater.product_interface:
    class: PrestaShopBundle\Service\DataUpdater\Admin\ProductInterface
  prestashop.core.admin.page_preference_interface:
    class: PrestaShopBundle\Service\TransitionalBehavior\AdminPagePreferenceInterface
  prestashop.core.admin.data_provider.module_interface:
    class: PrestaShopBundle\Service\DataProvider\Admin\ModuleInterface

  # URL generation
  prestashop.core.admin.url_generator_factory:
    class: PrestaShopBundle\Service\TransitionalBehavior\AdminUrlGeneratorFactory
    arguments:
      - "@router"

  prestashop.core.admin.url_generator_legacy:
    class: Symfony\Component\Routing\Generator\UrlGeneratorInterface
    factory: [ "@prestashop.core.admin.url_generator_factory", forLegacy ]
    arguments:
      - "@prestashop.adapter.legacy.context"

  prestashop.core.admin.url_generator:
    class: Symfony\Component\Routing\Generator\UrlGeneratorInterface
    factory: [ "@prestashop.core.admin.url_generator_factory", forSymfony ]

  prestashop.database.naming_strategy:
    class: PrestaShopBundle\Service\Database\DoctrineNamingStrategy
    arguments: [ "%database_prefix%" ]

  prestashop.service.product:
    class: PrestaShopBundle\Service\ProductService
    arguments:
      - "@prestashop.adapter.data_provider.product"
    deprecated: ~

  prestashop.service.translation:
    class: PrestaShopBundle\Service\TranslationService
    properties:
      container: "@service_container"

  # Interfaced services to decorate
  prestashop.core.data_provider.stock_interface:
    class: PrestaShopBundle\Service\DataProvider\StockInterface

  # DISPATCHERS
  prestashop.hook.dispatcher:
    class: PrestaShop\PrestaShop\Adapter\Hook\HookDispatcher
    arguments:
      - '@request_stack'
      - !tagged core.legacy.hook.subscriber
      - '@prestashop.hooks_registry'
      - '@=service("prestashop.adapter.environment").isDebug()'

  prestashop.hook.finder:
    class: PrestaShopBundle\Service\Hook\HookFinder

  # EVENT HANDLER
  prestashop.handler.log:
    class: PrestaShopBundle\Service\Log\LogHandler
    arguments:
      - "@service_container"

  # CSRF/XSS additional protection middleware
  prestashop.user_provider:
    class: PrestaShopBundle\Service\DataProvider\UserProvider
    arguments: [ '@security.token_storage' ]

  prestashop.router:
    class: PrestaShopBundle\Service\Routing\Router
    parent: 'router.default'
    public: true
    calls:
      - [ 'setTokenManager', [ '@security.csrf.token_manager' ] ]
      - [ 'setUserProvider', [ "@=service('prestashop.user_provider')" ] ]

  prestashop.multistore.customized_configuration_checker:
    class: PrestaShopBundle\Service\Multistore\CustomizedConfigurationChecker
    arguments:
      - "@prestashop.adapter.legacy.configuration"

  PrestaShopBundle\DependencyInjection\RuntimeConstEnvVarProcessor:
    public: false
    tags: [ 'container.env_var_processor' ]

  PrestaShopBundle\Api\StateProvider\HookStatusProvider:
    tags: [ 'api_platform.state_provider' ]
    public: false
    autowire: true
    arguments:
      - '@prestashop.core.query_bus'

  PrestaShopBundle\Security\Admin\SessionRenewer:
    arguments:
      $storage: "@security.csrf.token_storage"
    autowire: true
