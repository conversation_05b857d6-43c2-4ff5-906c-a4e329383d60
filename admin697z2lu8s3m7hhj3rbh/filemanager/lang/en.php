<?php

define('lang_Select', 'Select');
define('lang_Erase', 'Erase');
define('lang_Open', 'Open');
define('lang_Confirm_del', 'Are you sure you want to delete this file?');
define('lang_All', 'All');
define('lang_Files', 'Files');
define('lang_Images', 'Images');
define('lang_Archives', 'Archives');
define('lang_Error_Upload', 'The uploaded file exceeds the max size allowed.');
define('lang_Error_extension', 'File extension is not allowed.');
define('lang_Upload_file', 'Upload');
define('lang_Filters', 'Filters');
define('lang_Videos', 'Videos');
define('lang_Music', 'Music');
define('lang_New_Folder', 'New Folder');
define('lang_Folder_Created', 'Folder correctly created');
define('lang_Existing_Folder', 'Existing folder');
define('lang_Confirm_Folder_del', 'Are you sure to delete the folder and all the elements in it?');
define('lang_Return_Files_List', 'Return to files list');
define('lang_Preview', 'Preview');
define('lang_Download', 'Download');
define('lang_Insert_Folder_Name', 'Insert folder name:');
define('lang_Root', 'root');
define('lang_Rename', 'Rename');
define('lang_Back', 'back');
define('lang_View', 'View');
define('lang_View_list', 'List view');
define('lang_View_columns_list', 'Columns list view');
define('lang_View_boxes', 'Box view');
define('lang_Toolbar', 'Toolbar');
define('lang_Actions', 'Actions');
define('lang_Rename_existing_file', 'The file is already existing');
define('lang_Rename_existing_folder', 'The folder is already existing');
define('lang_Empty_name', 'The name is empty');
define('lang_Text_filter', 'text filter');
define('lang_Swipe_help', 'Swipe the name of file/folder to show options');
define('lang_Upload_base', 'Base upload');
define('lang_Upload_java', 'JAVA upload (big size files)');
define('lang_Upload_java_help', "If the Java Applet doesn't load, 1. make sure you have Java installed, otherwise <a href='http://java.com/en/download/'>[download link]</a>   2. make sure nothing is blocked by your firewall");
define('lang_Upload_base_help', "Drag & Drop files or click in the area above (modern browsers) and select the file(s). When the upload is complete, click the 'Return to files list' button.");
define('lang_Type_dir', 'dir');
define('lang_Type', 'Type');
define('lang_Dimension', 'Dimension');
define('lang_Size', 'Size');
define('lang_Date', 'Date');
define('lang_Filename', 'Filename');
define('lang_Operations', 'Operations');
define('lang_Date_type', 'y-m-d');
define('lang_OK', 'OK');
define('lang_Cancel', 'Cancel');
define('lang_Sorting', 'sorting');
define('lang_Show_url', 'show URL');
define('lang_Extract', 'extract here');
define('lang_File_info', 'file info');
define('lang_Edit_image', 'edit image');
define('lang_Duplicate', 'Duplicate');
