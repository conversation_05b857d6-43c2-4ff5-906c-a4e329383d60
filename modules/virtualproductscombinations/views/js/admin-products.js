/*
*
* NOTICE OF LICENSE
*
* This source file is subject to the Open Software License (OSL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/osl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Do not edit or add to this file if you wish to upgrade PrestaShop to newer
* versions in the future. If you wish to customize PrestaShop for your
* needs please refer to http://www.prestashop.com for more information.
*
*  <AUTHOR> SA <<EMAIL>>
*  @copyright  2007-2018 PrestaShop SA
*  @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
*  International Registered Trademark & Property of PrestaShop SA
*/

$(document).ready(function() {
    var virtualProductsCombinations = (function() {
      var id_product = $('#form_id_product').val();
      var productTypeSelector = $('#form_step1_type_product');

      var getOnDeleteVirtualProductFileHandler = function ($deleteButton) {
        var idProductAttribute = parseInt($deleteButton.data('id-product-attribute'));
        var deleteUrl = $deleteButton.attr('href');
        
        console.log('Deleting virtual product file:');
        console.log('- Product attribute ID:', idProductAttribute);
        console.log('- Delete URL:', deleteUrl);
        
        return $.ajax({
          type: 'GET',
          url: deleteUrl,
          success: function (response) {
            console.log('Delete successful:', response);
            $('#form_step3_virtual_product_file_input_'+idProductAttribute).removeClass('hide').addClass('show');
            $('#form_step3_virtual_product_file_details_'+idProductAttribute).removeClass('show').addClass('hide');
          },
          error: function (xhr, status, error) {
            console.error('Delete failed:', {
              status: status,
              error: error,
              response: xhr.responseText
            });
          }
        })
      };

      var processSaveVirtualProductFile = function(button) {
        console.log('processSaveVirtualProductFile called with button:', button);
        
        var _this = $(button);
        var data = new FormData();
        var idProductAttribute = parseInt(_this.data('id-product-attribute'));
        
        console.log('Processing save for product attribute:', idProductAttribute);

        if ($('#form_step3_virtual_product_file_'+idProductAttribute)[0] && $('#form_step3_virtual_product_file_'+idProductAttribute)[0].files[0]) {
          data.append('product_virtual[file]', $('#form_step3_virtual_product_file_'+idProductAttribute)[0].files[0]);
          console.log('File attached:', $('#form_step3_virtual_product_file_'+idProductAttribute)[0].files[0].name);
        } else {
          console.log('No file attached');
        }
        
        // Check for virtual file setting in both PrestaShop 8 and 1.7 structures
        var isVirtualFile = '1'; // Default to true since we're in the combinations context
        if ($('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').length > 0) {
          isVirtualFile = $('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').val();
        } else if ($('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').length > 0) {
          isVirtualFile = $('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').val();
        }
        
        data.append('product_virtual[is_virtual_file]', isVirtualFile);
        data.append('product_virtual[name]', $('#form_step3_virtual_product_name_'+idProductAttribute).val());
        data.append('product_virtual[nb_downloadable]', $('#form_step3_virtual_product_nb_downloadable_'+idProductAttribute).val());
        data.append('product_virtual[expiration_date]', $('#form_step3_virtual_product_expiration_date_'+idProductAttribute).val());
        data.append('product_virtual[nb_days]', $('#form_step3_virtual_product_nb_days_'+idProductAttribute).val());

        var saveUrl = $('#form_step3_virtual_product_save_link_'+idProductAttribute).val();
        
        console.log('Form data values:');
        console.log('- is_virtual_file:', isVirtualFile);
        console.log('- name:', $('#form_step3_virtual_product_name_'+idProductAttribute).val());
        console.log('- nb_downloadable:', $('#form_step3_virtual_product_nb_downloadable_'+idProductAttribute).val());
        console.log('- expiration_date:', $('#form_step3_virtual_product_expiration_date_'+idProductAttribute).val());
        console.log('- nb_days:', $('#form_step3_virtual_product_nb_days_'+idProductAttribute).val());
        console.log('- saveUrl:', saveUrl);
        
        if (!saveUrl) {
          console.error('Save URL is empty!');
          alert('Error: Save URL is not available');
          return;
        }
        
        console.log('Starting AJAX request...');
        
        $.ajax({
          type: 'POST',
          url: saveUrl,
          data: data,
          contentType: false,
          processData: false,
          beforeSend: function() {
            console.log('AJAX beforeSend triggered');
            _this.prop('disabled', 'disabled');
            $('ul.text-danger').remove();
            $('*.has-danger').removeClass('has-danger');
          },
          success: function(response) {
            console.log('AJAX success:', response);
            // Handle success message
            if (typeof showSuccessMessage === 'function') {
              showSuccessMessage(translate_javascripts['Form update success'] || 'Successfully saved');
            } else {
              alert('Successfully saved');
            }
            
            if (response.file_download_link) {
              $('#form_step3_virtual_product_file_details_'+idProductAttribute+' a.download').attr('href', response.file_download_link);
              $('#form_step3_virtual_product_file_input_'+idProductAttribute).removeClass('show').addClass('hide');
              $('#form_step3_virtual_product_file_details_'+idProductAttribute).removeClass('hide').addClass('show');
            }
          },
          error: function(xhr, status, error) {
            console.log('AJAX error:', xhr, status, error);
            console.log('Response text:', xhr.responseText);
            
            try {
              var errorData = jQuery.parseJSON(xhr.responseText);
              $.each(errorData, function(key, errors) {
                var html = '<ul class="list-unstyled text-danger">';
                $.each(errors, function(key, error) {
                  html += '<li>' + error + '</li>';
                });
                html += '</ul>';

                $('#form_step3_virtual_product_' + key).parent().append(html);
                $('#form_step3_virtual_product_' + key).parent().addClass('has-danger');
              });
            } catch (e) {
              console.error('Could not parse error response:', e);
              alert('Error saving file: ' + error);
            }
          },
          complete: function() {
            console.log('AJAX complete');
            _this.removeAttr('disabled');
          }
        });
      };

      return {
        'init': function() {
            productTypeSelector.prop('disabled', false);
            if (typeof vpc_display_admin_products_extra !== "undefined") {
              $('#virtual_product_content').replaceWith(vpc_display_admin_products_extra);
            }
            // Toggle virtual checkbox
            $(document).off('change', '#vpc_toggle_virtual').on('change', '#vpc_toggle_virtual', function() {
              if (typeof vpc_toggle_virtual_link === 'undefined') {
                return;
              }
              var checked = $(this).is(':checked') ? 1 : 0;
              $.ajax({
                type: 'POST',
                url: vpc_toggle_virtual_link + '&state=' + checked + '&ajax=1',
                dataType: 'json',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
                success: function() {
                  if (typeof showSuccessMessage === 'function') {
                    showSuccessMessage(checked ? 'Product marked as virtual' : 'Product set to non-virtual');
                  }
                },
                error: function(xhr) {
                  console.error('Toggle virtual error:', xhr && xhr.responseText ? xhr.responseText : xhr);
                  if (typeof showErrorMessage === 'function') {
                    showErrorMessage('Could not change virtual state');
                  }
                }
              });
            });

            // Check for PrestaShop 8 structure first
            if ($('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').length > 0) {
              // PrestaShop 8 structure
              if ($('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').val() === '1') {
                $('#virtual_product_content').show();
              } else {
                $('#virtual_product_content').hide();
              }
            } else if ($('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').length > 0) {
              // PrestaShop 1.7 structure (fallback)
              if ($('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').val() === '1') {
                $('#virtual_product_content').show();
              } else {
                $('#virtual_product_content').hide();
              }
            } else {
              // Force show if we have combinations and virtual product is enabled
              if ($('#virtual_product_content').length > 0 && $('#virtual_product_content').html().trim() !== '') {
                $('#virtual_product_content').show();
              }
            }

            // Handle changes for PrestaShop 8
            $(document).on('change', 'input[name="form[stock][virtual_product][is_virtual_file]"]', function() {
              if ($(this).val() === '1') {
                $('#virtual_product_content').show();
              } else {
                $('#virtual_product_content').hide();
              }
            });

            // Handle changes for PrestaShop 1.7 (fallback)
            $(document).on('change', 'input[name="form[step3][virtual_product][is_virtual_file]"]', function() {
              if ($(this).val() === '1') {
                $('#virtual_product_content').show();
              } else {
                $('#virtual_product_content').hide();
              }
            });

            // Monitor product type changes in PrestaShop 8
            $(document).on('change', 'select[name="form[header][type]"]', function() {
              var productType = $(this).val();
              // Only show for products with combinations (type 0 with combinations)
              if (productType == '0') {
                // Check if product has combinations
                setTimeout(function() {
                  virtualProductsCombinations.refreshVirtualProductsForm();
                }, 500);
              }
            });

            // Monitor product type changes in PrestaShop 1.7 (fallback)
            $(document).on('change', 'input[name="form[step1][type_product]"]', function() {
              var productType = $(this).val();
              if (productType == '0') {
                setTimeout(function() {
                  virtualProductsCombinations.refreshVirtualProductsForm();
                }, 500);
              }
            });

            $(document).on('change', 'input[name="form[step3][virtual_product][is_virtual_file]"]', function() {
              if ($(this).val() === '1') {
                $('#virtual_product_content').show();
              } else {
                $('#virtual_product_content').hide();

                /* @TODO */
                /*
                var url = $('#virtual_product').attr('data-action-remove').replace(/remove\/\d+/, 'remove/' + id_product);
                //delete virtual product
                $.ajax({
                  type: 'GET',
                  url: url,
                  success: function() {
                    //empty form
                    $('#form_step3_virtual_product_file_input').removeClass('hide').addClass('show');
                    $('#form_step3_virtual_product_file_details').removeClass('show').addClass('hide');
                    $('#form_step3_virtual_product_name').val('');
                    $('#form_step3_virtual_product_nb_downloadable').val(0);
                    $('#form_step3_virtual_product_expiration_date').val('');
                    $('#form_step3_virtual_product_nb_days').val(0);
                  }
                });
                */
              }
            });

            $(document).on('change', '#form_step3_virtual_product_file', function(e) {
              if ($(this)[0].files !== undefined) {
                var files = $(this)[0].files;
                var name  = '';

                $.each(files, function(index, value) {
                  name += value.name + ', ';
                });
                $('#form_step3_virtual_product_name').val(name.slice(0, -2));
              } else {
                // Internet Explorer 9 Compatibility
                var name = $(this).val().split(/[\\/]/);
                $('#form_step3_virtual_product_name').val(name[name.length - 1]);
              }
            });

            /** delete attached file */
            $('#form_step3_virtual_product_file_details .delete').click(function(e) {
              e.preventDefault();
              var $deleteButton = $(this);

              // Try different ways to access modalConfirmation
              var modalConfirmationObj = null;
              if (typeof window.modalConfirmation !== 'undefined') {
                modalConfirmationObj = window.modalConfirmation;
              } else if (typeof modalConfirmation !== 'undefined') {
                modalConfirmationObj = modalConfirmation;
              }

              if (modalConfirmationObj) {
                modalConfirmationObj.create(translate_javascripts['Are you sure to delete this?'] || 'Are you sure to delete this?', null, {
                  onContinue: function() {
                    getOnDeleteVirtualProductFileHandler($deleteButton);
                  }
                }).show();
              } else {
                // Fallback to native confirm dialog
                if (confirm('Are you sure to delete this?')) {
                  getOnDeleteVirtualProductFileHandler($deleteButton);
                }
              }
            });

            /** save virtual product */
            $('#form_step3_virtual_product_save').click(function() {
              console.log('form_step3_virtual_product_save');
              var _this = $(this);
              var data = new FormData();

              if ($('#form_step3_virtual_product_file')[0].files[0]) {
                data.append('product_virtual[file]', $('#form_step3_virtual_product_file')[0].files[0]);
              }
              data.append('product_virtual[is_virtual_file]', $('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').val());
              data.append('product_virtual[name]', $('#form_step3_virtual_product_name').val());
              data.append('product_virtual[nb_downloadable]', $('#form_step3_virtual_product_nb_downloadable').val());
              data.append('product_virtual[expiration_date]', $('#form_step3_virtual_product_expiration_date').val());
              data.append('product_virtual[nb_days]', $('#form_step3_virtual_product_nb_days').val());

              $.ajax({
                type: 'POST',
                url: $('#virtual_product').attr('data-action').replace(/save\/\d+/, 'save/' + id_product),
                data: data,
                contentType: false,
                processData: false,
                beforeSend: function() {
                  _this.prop('disabled', 'disabled');
                  $('ul.text-danger').remove();
                  $('*.has-danger').removeClass('has-danger');
                },
                success: function(response) {
                  showSuccessMessage(translate_javascripts['Form update success']);
                  if (response.file_download_link) {
                    $('#form_step3_virtual_product_file_details a.download').attr('href', response.file_download_link);
                    $('#form_step3_virtual_product_file_input').removeClass('show').addClass('hide');
                    $('#form_step3_virtual_product_file_details').removeClass('hide').addClass('show');
                  }
                },
                error: function(response) {
                  $.each(jQuery.parseJSON(response.responseText), function(key, errors) {
                    var html = '<ul class="list-unstyled text-danger">';
                    $.each(errors, function(key, error) {
                      html += '<li>' + error + '</li>';
                    });
                    html += '</ul>';

                    $('#form_step3_virtual_product_' + key).parent().append(html);
                    $('#form_step3_virtual_product_' + key).parent().addClass('has-danger');
                  });
                },
                complete: function() {
                  _this.removeAttr('disabled');
                }
              });
            });

            $(document).on('change', '.form_step3_virtual_product_file', function(e) {
              var idProductAttribute = parseInt($(this).data('id-product-attribute'));
              if ($(this)[0].files !== undefined) {
                var files = $(this)[0].files;
                var name  = '';

                $.each(files, function(index, value) {
                  name += value.name + ', ';
                });
                $('#form_step3_virtual_product_name_'+idProductAttribute).val(name.slice(0, -2));
              } else {
                // Internet Explorer 9 Compatibility
                var name = $(this).val().split(/[\\/]/);
                $('#form_step3_virtual_product_name_'+idProductAttribute).val(name[name.length - 1]);
              }
            });

            $(document).on('click', '.form_step3_virtual_product_file_details .delete', function(e) {
              e.preventDefault();
              var $deleteButton = $(this);
              
              console.log('Delete button clicked');

              // Try different ways to access modalConfirmation
              var modalConfirmationObj = null;
              if (typeof window.modalConfirmation !== 'undefined') {
                modalConfirmationObj = window.modalConfirmation;
              } else if (typeof modalConfirmation !== 'undefined') {
                modalConfirmationObj = modalConfirmation;
              }

              if (modalConfirmationObj) {
                modalConfirmationObj.create(translate_javascripts['Are you sure to delete this?'] || 'Are you sure to delete this?', null, {
                  onContinue: function() {
                    console.log('Delete confirmed');
                    getOnDeleteVirtualProductFileHandler($deleteButton);
                  }
                }).show();
              } else {
                // Fallback to native confirm dialog
                if (confirm('Are you sure to delete this?')) {
                  console.log('Delete confirmed via native dialog');
                  getOnDeleteVirtualProductFileHandler($deleteButton);
                }
              }
            });

            // Try multiple selectors to catch the save button
            $(document).on('click', '.form_step3_virtual_product_save, button[name*="virtual_product"][name*="save"]', function(e) {
              console.log('Save button clicked via event delegation');
              e.preventDefault(); // Prevent any default behavior
              
              // Additional debugging
              console.log('Button element:', $(this));
              console.log('Button classes:', $(this).attr('class'));
              console.log('Button data attributes:', $(this).data());
              
              processSaveVirtualProductFile(this);
            });
            
            // Test event delegation for any button in virtual product content
            $(document).on('click', '#virtual_product_content button', function(e) {
              console.log('Any button clicked in virtual product content:', $(this));
              console.log('Button classes:', $(this).attr('class'));
              console.log('Button name:', $(this).attr('name'));
            });
            
            // Add a direct event binding as a fallback - bind after content is loaded
            setTimeout(function() {
              $('.form_step3_virtual_product_save').each(function() {
                console.log('Found save button:', $(this));
                console.log('Button ID:', $(this).attr('id'));
                console.log('Button classes:', $(this).attr('class'));
                console.log('Button data-id-product-attribute:', $(this).data('id-product-attribute'));
                console.log('Button CSS display:', $(this).css('display'));
                console.log('Button CSS visibility:', $(this).css('visibility'));
                console.log('Button CSS pointer-events:', $(this).css('pointer-events'));
                console.log('Button is visible:', $(this).is(':visible'));
                
                // Add a direct click handler for testing
                $(this).off('click.direct').on('click.direct', function(e) {
                  console.log('Direct click handler triggered on button:', $(this));
                  e.preventDefault();
                  e.stopPropagation();
                  
                  // Call the save function directly
                  processSaveVirtualProductFile(this);
                });
              });
            }, 1000);
        }
      };
    })();

    /*
     * virtual product management
    */
    virtualProduct = (function() {
      var id_product = $('#form_id_product').val();

      return {
        'init': function() {
        },
        /* @TODO */
        'destroy': function () {
          var fileDetailsSelector = '#form_step3_virtual_product_file_details';
          var fileAssociationExists = !$(fileDetailsSelector).hasClass('hide');

          if (fileAssociationExists) {
            var $deleteButton = $(fileDetailsSelector + ' .delete');
            getOnDeleteVirtualProductFileHandler($deleteButton);
          }

          var associatedFileCheckboxSelectorPrefix = '#form_step3_virtual_product_is_virtual_file_';
          $(associatedFileCheckboxSelectorPrefix + '0').prop('checked', false);
          $(associatedFileCheckboxSelectorPrefix + '1').prop('checked', true);

          $('#virtual_product_content input').val('');
        }
      }
    })();

    displayFieldsManager = (function() {

      var typeProduct = $('#form_step1_type_product');
      var showVariationsSelector = $('#show_variations_selector');
      var productTypeSelector = $('#form_step1_type_product');
      var combinationsBlock = $('#combinations');

      return {
        'init': function () {
        },
        /**
         * When a product is available for order, its price should be visible,
         * whereas products unavailable for order can have their prices visible or hidden.
         */
        'initVisibilityRule': function () {
          var showPriceSelector = '.js-show-price';
          var availableForOrderSelector = '.js-available-for-order';

          var applyVisibilityRule = function applyVisibilityRule() {
            var $availableForOrder = $(availableForOrderSelector + ' input');
            var $showPrice = $(showPriceSelector + ' input');
            var $showPriceColumn = $(showPriceSelector);
            if ($availableForOrder.prop('checked')) {
              $showPrice.prop('checked', true);
              $showPriceColumn.addClass('hide');
            } else {
              $showPriceColumn.removeClass('hide');
            }
          };
          $(availableForOrderSelector + ' .checkbox').on('click', applyVisibilityRule);
          applyVisibilityRule();
        },
        'refresh': function () {
          this.checkAccessVariations();
          $('#virtual_product').hide();
          $('#form-nav a[href="#step3"]').text(translate_javascripts['Quantities']);

          /** product type switch */

          if (typeProduct.val() === '1') {
            $('#pack_stock_type, #js_form_step1_inputPackItems').show();
            $('#form-nav a[href="#step4"]').show();
            showVariationsSelector.hide();
            showVariationsSelector.find('input[value="0"]').attr('checked', true);
          } else {
            $('#virtual_product, #pack_stock_type, #js_form_step1_inputPackItems').hide();
            $('#form-nav a[href="#step4"]').show();

            if (typeProduct.val() === '2') {
              showVariationsSelector.show();
              $('#virtual_product').show();
              $('#form-nav a[href="#step4"]').hide();
              //showVariationsSelector.find('input[value="0"]').attr('checked', true);
              if (showVariationsSelector.find('input:checked').val() === '0') {
                $('#form-nav a[href="#step3"]').text(translate_javascripts['Virtual product']);
              } else {
                $('#form-nav a[href="#step3"]').text(translate_javascripts['Combinations'] + ' & ' + translate_javascripts['Virtual product']);
              }
              this.refreshVirtualProductsForm();
            } else {
              showVariationsSelector.show();
              $('#form-nav a[href="#step3"]').text(translate_javascripts['Quantities']);
            }
          }

          /** check quantity / combinations display */
          if (showVariationsSelector.find('input:checked').val() === '1' || $('#accordion_combinations tr:not(#loading-attribute)').length > 0) {
            combinationsBlock.show();

            $('#specific-price-combination-selector').removeClass('hide').show();
            if (typeProduct.val() === '0') {
              $('#form-nav a[href="#step3"]').text(translate_javascripts['Combinations']);
            } else {
              if (showVariationsSelector.find('input:checked').val() === '0') {
                $('#form-nav a[href="#step3"]').text(translate_javascripts['Virtual product']);
              } else {
                $('#form-nav a[href="#step3"]').text(translate_javascripts['Combinations'] + ' & ' + translate_javascripts['Virtual product']);
              }
            }
            $('#product_qty_0_shortcut_div, #quantities').hide();
          } else {
            combinationsBlock.hide();
            $('#specific-price-combination-selector').hide();
            $('#product_qty_0_shortcut_div, #quantities').show();
          }

          /** Tooltip for product type combinations */
          if ($('input[name="show_variations"][value="1"]:checked').length >= 1) {
            $('#product_type_combinations_shortcut').show();
          } else {
            $('#product_type_combinations_shortcut').hide();
          }
        },
        'getProductType': function () {
          switch (typeProduct.val()) {
            case '0':
              return 'standard';
              break;
            case '1':
              return 'pack';
              break;
            case '2':
              return 'virtual';
              break;
            default:
              return 'standard';
          }
        },
        /**
         * Product pack can't have variations
         * Warn e-merchant.
         * @param errorMessage
         */
        'checkAccessVariations': function () {
          if ((showVariationsSelector.find('input:checked').val() === '1' || $('#accordion_combinations tr:not(#loading-attribute)').length > 0) && (typeProduct.val() === '1')) {
            var typeOfProduct = this.getProductType();
            var errorMessage = "You can't create " + typeOfProduct + " product with variations. Are you sure to disable variations ? they will all be deleted.";
            
            // Try different ways to access modalConfirmation
            var modalConfirmationObj = null;
            if (typeof window.modalConfirmation !== 'undefined') {
              modalConfirmationObj = window.modalConfirmation;
            } else if (typeof modalConfirmation !== 'undefined') {
              modalConfirmationObj = modalConfirmation;
            }

            if (modalConfirmationObj) {
              modalConfirmationObj.create(translate_javascripts[errorMessage] || errorMessage, null, {
              onCancel: function () {
                typeProduct.val(0).change();
                /* else the radio bouton is not display even if checked attribute is true */
                $('#show_variations_selector input[value="1"]').click();
                productTypeSelector.prop('disabled', false);
              },
              onContinue: function () {
                $.ajax({
                  type: 'GET',
                  url: $('#accordion_combinations').attr('data-action-delete-all').replace(/delete-all\/\d+/, 'delete-all/' + $('#form_id_product').val()),
                  success: function () {
                    $('#accordion_combinations .combination').remove();
                    this.refresh();
                    productTypeSelector.prop('disabled', false);
                  },
                  error: function (response) {
                    showErrorMessage(jQuery.parseJSON(response.responseText).message);
                  },
                });
              }
            }).show();
            } else {
              // Fallback to native confirm dialog
              if (confirm(errorMessage)) {
                $.ajax({
                  type: 'GET',
                  url: $('#accordion_combinations').attr('data-action-delete-all').replace(/delete-all\/\d+/, 'delete-all/' + $('#form_id_product').val()),
                  success: function () {
                    $('#accordion_combinations .combination').remove();
                    this.refresh();
                    productTypeSelector.prop('disabled', false);
                  },
                  error: function (response) {
                    showErrorMessage(jQuery.parseJSON(response.responseText).message);
                  },
                });
              } else {
                typeProduct.val(0).change();
                $('#show_variations_selector input[value="1"]').click();
                productTypeSelector.prop('disabled', false);
              }
            }
          }
        },
        'refreshVirtualProductsForm': function() {
          $.ajax({
            type: 'GET',
            url: vpc_configuration_link,
            success: function (response) {
              if (response.content != '') {
                $('#virtual_product_content').replaceWith(response.content);
                
                // Always show the content if it has combinations
                $('#virtual_product_content').show();
                
                // Check for PrestaShop 8 structure first
                if ($('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').length > 0) {
                  if ($('input[name="form[stock][virtual_product][is_virtual_file]"]:checked').val() === '1') {
                    $('#virtual_product_content').show();
                  } else {
                    $('#virtual_product_content').hide();
                  }
                } else if ($('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').length > 0) {
                  // PrestaShop 1.7 fallback
                  if ($('input[name="form[step3][virtual_product][is_virtual_file]"]:checked').val() === '1') {
                    $('#virtual_product_content').show();
                  } else {
                    $('#virtual_product_content').hide();
                  }
                }
                
                // Debug the refreshed content
                console.log('Virtual product content refreshed');
                console.log('Save buttons found:', $('.form_step3_virtual_product_save').length);
                $('.form_step3_virtual_product_save').each(function() {
                  console.log('Save button:', $(this));
                });
                
                virtualProduct.init();
              }
            }
          })
        }
      }
    })();

    /**
     * Combination management
     */
    combinations = (function() {
      var id_product = $('#form_id_product').val();

      /**
       * Remove a combination
       * @param {object} elem - The clicked link
       */
      function remove(elem) {
        var combinationElem = $('#attribute_' + elem.attr('data'));

        // Try different ways to access modalConfirmation
        var modalConfirmationObj = null;
        if (typeof window.modalConfirmation !== 'undefined') {
          modalConfirmationObj = window.modalConfirmation;
        } else if (typeof modalConfirmation !== 'undefined') {
          modalConfirmationObj = modalConfirmation;
        }

        if (modalConfirmationObj) {
          modalConfirmationObj.create(translate_javascripts['Are you sure to delete this?'] || 'Are you sure to delete this?', null, {
            onContinue: function() {

            var attributeId = elem.attr('data');
            $.ajax({
              type: 'DELETE',
              data: {'attribute-ids': [attributeId]},
              url: elem.attr('href'),
              beforeSend: function() {
                elem.attr('disabled', 'disabled');
              },
              success: function(response) {
                combinationElem.remove();
                showSuccessMessage(response.message);
                displayFieldsManager.refresh();
              },
              error: function(response) {
                showErrorMessage(jQuery.parseJSON(response.responseText).message);
              },
              complete: function() {
                elem.removeAttr('disabled');
                supplierCombinations.refresh();
                warehouseCombinations.refresh();
                if ($('.js-combinations-list .combination').length <= 0) {
                  $('#combinations_thead').fadeOut();
                }
              }
            });
          }
        }).show();
        } else {
          // Fallback to native confirm dialog
          if (confirm('Are you sure to delete this?')) {
            var attributeId = elem.attr('data');
            $.ajax({
              type: 'DELETE',
              data: {'attribute-ids': [attributeId]},
              url: elem.attr('href'),
              beforeSend: function() {
                elem.attr('disabled', 'disabled');
              },
              success: function(response) {
                combinationElem.remove();
                showSuccessMessage(response.message);
                displayFieldsManager.refresh();
              },
              error: function(response) {
                showErrorMessage(jQuery.parseJSON(response.responseText).message);
              },
              complete: function() {
                elem.removeAttr('disabled');
                supplierCombinations.refresh();
                warehouseCombinations.refresh();
                if ($('.js-combinations-list .combination').length <= 0) {
                  $('#combinations_thead').fadeOut();
                }
              }
            });
          }
        }
      }

      /**
       * Update final price, regarding the impact on price in combinations table
       * @param {object} elem - The tableau row parent
       */
      function updateFinalPrice(tableRow) {
          if (!tableRow.is('tr')) {
              throw new Error('Structure of table has changed, this function need to be updated.');
          }
          var priceImpactInput = tableRow.find('.attribute_priceTE');
          var impactOnPrice = priceImpactInput.val() - priceImpactInput.attr('value');
          var actualFinalPriceInput = tableRow.find('.attribute-finalprice span');
          var actualFinalPrice = actualFinalPriceInput.data('price');

          var finalPrice = new Number(actualFinalPrice) + new Number(impactOnPrice);
          actualFinalPriceInput.html(ps_round(finalPrice, 6));
      }

      return {
        'init': function() {
          var showVariationsSelector = $('#show_variations_selector input');
          var productTypeSelector = $('#form_step1_type_product');
          var combinationsListSelector = '#accordion_combinations .combination';
          var combinationsList = $(combinationsListSelector);

          if (combinationsList.length > 0) {
            productTypeSelector.prop('disabled', false);
          }

          /** delete combination */
          $(document).on('click', '#accordion_combinations .delete', function(e) {
            e.preventDefault();
            remove($(this));
          });

          /** on change quantity, update field quantity row */
          $(document).on('keyup', 'input[id^="combination"][id$="_attribute_quantity"]', function() {
            var id_attribute = $(this).closest('.combination-form').attr('data');
            $('#accordion_combinations #attribute_' + id_attribute).find('.attribute-quantity input').val($(this).val());
          });

          /** on change shortcut quantity, update form field quantity */
          $(document).on('keyup', '.attribute-quantity input', function() {
            var id_attribute = $(this).closest('.combination').attr('data');
            $('#combination_form_' + id_attribute).find('input[id^="combination"][id$="_attribute_quantity"]').val($(this).val());
          });

          /** on change shortcut impact on price, update form field impact on price */
          $(document).on('keyup', 'input[id^="combination"][id$="_attribute_price"]', function () {
            var id_attribute = $(this).closest('.combination-form').attr('data');
            var input = $('#accordion_combinations #attribute_' + id_attribute).find('.attribute-price input');

            input.val($(this).val());

            /* force the update of final price */
            updateFinalPrice($(input.parents('tr')[0]));
          });

          /** on change default attribute, update which combination is the new default */
          $(document).on('click', 'input.attribute-default', function() {
            var selectedCombination = $(this);
            var combinationRadioButtons = $('input.attribute-default');
            var id_attribute = $(this).closest('.combination').attr('data');

            combinationRadioButtons.each(function unselect(index) {
              var combination = $(this);
              if(combination.data('id') !== selectedCombination.data('id')) {
                combination.prop("checked", false);
              }
            });


            $('.attribute_default_checkbox').removeAttr('checked');
            $('#combination_form_' + id_attribute).find('input[id^="combination"][id$="_attribute_default"]').prop("checked", true);
          });


          /** on change price on impact, update price on impact form field */
          $(document).on('change', '.attribute-price input', function() {
            var id_attribute = $(this).closest('.combination').attr('data');
            $('#combination_form_' + id_attribute).find('input[id^="combination"][id$="_attribute_price"]').val($(this).val());
            updateFinalPrice($(this).parent().parent().parent());
          });

          /** on change price, update price row */
          $(document).on('keyup', 'input[id^="combination"][id$="_attribute_price"]', function() {
            var id_attribute = $(this).closest('.combination-form').attr('data');
            var attributePrice = $('#accordion_combinations #attribute_' + id_attribute).find('.attribute-price-display');
            formatCurrencyCldr(parseFloat($(this).val()), function(result) {
              attributePrice.html(result);
            });
          });

          /** Combinations fields display management */
          showVariationsSelector.change(function() {
            displayFieldsManager.refresh();
            combinationsList = $(combinationsListSelector);

            if ($(this).val() === '0') {
              //if combination(s) exists, alert user for deleting it
              if (combinationsList.length > 0) {
                modalConfirmation.create(translate_javascripts['Are you sure to disable variations ? they will all be deleted'], null, {
                  onCancel: function() {
                    $('#show_variations_selector input[value="1"]').prop('checked', true);
                    displayFieldsManager.refresh();
                  },
                  onContinue: function() {
                    $.ajax({
                      type: 'GET',
                      url: $('#accordion_combinations').attr('data-action-delete-all').replace(/\/\d+(?=\?.*)/, '/' + $('#form_id_product').val()),
                      success: function(response) {
                        combinationsList.remove();
                        displayFieldsManager.refresh();
                      },
                      error: function(response) {
                        showErrorMessage(jQuery.parseJSON(response.responseText).message);
                      },
                    });
                    // enable the top header selector
                    // we want to use a "Simple product" without any combinations
                    productTypeSelector.prop('disabled', false);
                  }
                }).show();
              } else {
                // enable the top header selector if no combination(s) exists
                productTypeSelector.prop('disabled', false);
              }
            } else {
              // this means we have or we want to have combinations
              // disable the product type selector
              productTypeSelector.prop('disabled', false);
            }
          });

          /** open combination form */
          $(document).on('click', '#accordion_combinations .btn-open', function(e) {
            e.preventDefault();
            var contentElem = $($(this).attr('href'));

            /** create combinations navigation */
            var navElem = contentElem.find('.nav');
            var id_attribute = contentElem.attr('data');
            var prevCombinationId = $('#accordion_combinations tr[data="' + id_attribute + '"]').prev().attr('data');
            var nextCombinationId = $('#accordion_combinations tr[data="' + id_attribute + '"]').next().attr('data');
            navElem.find('.prev, .next').hide();
            if (prevCombinationId) {
              navElem.find('.prev').attr('data', prevCombinationId).show();
            }
            if (nextCombinationId) {
              navElem.find('.next').attr('data', nextCombinationId).show();
            }

            /** init combination tax include price */
            priceCalculation.impactTaxInclude(contentElem.find('.attribute_priceTE'));

            contentElem.insertBefore('#form-nav').removeClass('hide').show();

            contentElem.find('.datepicker').datetimepicker({
              locale: iso_user,
              format: 'YYYY-MM-DD'
            });

            function countSelectedProducts() {
              return $('#combination_form_' + contentElem.attr('data') + ' .img-highlight').length;
            }

            var number = $('#combination_form_' + contentElem.attr('data') + ' .number-of-images'),
                allProductCombination = $('#combination_form_' + contentElem.attr('data') + ' .product-combination-image').length;

            number.text(countSelectedProducts() + '/' + allProductCombination);

            $(document).on('click','.tabs .product-combination-image', function () {
              number.text(countSelectedProducts() + '/' + allProductCombination);
            });

            $('#form-nav, #form_content').hide();
          });

          /** close combination form */
          $(document).on('click', '#form .combination-form .btn-back', function(e) {
            e.preventDefault();
            $(this).closest('.combination-form').hide();
            $('#form-nav, #form_content').show();
          });

          /** switch combination form */
          $(document).on('click', '#form .combination-form .nav a', function(e) {
            e.preventDefault();
            $('.combination-form').hide();
            $('#accordion_combinations .combination[data="' + $(this).attr('data') + '"] .btn-open').click();
          });
        }
      };
    })();

    // Initialize immediately when document is ready
    virtualProductsCombinations.init();
    
    // Also bind to the BOEvent for compatibility
    BOEvent.on("Product Combinations Management started", function initVirtualProductsCombinationsManagement() {
            console.log("BOEvent: Product Combinations Management started");
            virtualProductsCombinations.init();
    }, "VirtualProductsCombinations");
    
    // Additional initialization for debugging
    console.log("VirtualProductsCombinations script loaded");
    console.log("Current page controller:", typeof window.controller !== 'undefined' ? window.controller : 'undefined');
    console.log("Virtual product content exists:", $('#virtual_product_content').length > 0);
    
    // Force initialization after a delay to ensure DOM is ready
    setTimeout(function() {
        console.log("Delayed initialization");
        virtualProductsCombinations.init();
    }, 2000);
    
    // Expose a global function for testing
    window.testVirtualProductSave = function(idProductAttribute) {
        console.log('Testing save for attribute:', idProductAttribute);
        var button = $('.form_step3_virtual_product_save[data-id-product-attribute="' + idProductAttribute + '"]');
        if (button.length > 0) {
            console.log('Found button:', button);
            button.click();
        } else {
            console.error('Button not found for attribute:', idProductAttribute);
        }
    };
});
