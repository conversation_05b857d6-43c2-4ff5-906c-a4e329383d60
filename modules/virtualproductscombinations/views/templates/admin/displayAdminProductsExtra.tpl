{*
* 2007-2018 PrestaShop
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/afl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Do not edit or add to this file if you wish to upgrade PrestaShop to newer
* versions in the future. If you wish to customize PrestaShop for your
* needs please refer to http://www.prestashop.com for more information.
*
*  <AUTHOR> SA <<EMAIL>>
*  @copyright  2007-2018 PrestaShop SA
*  @license    http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*  International Registered Trademark & Property of PrestaShop SA
*}
<div id="virtual_product_content" class="row col-md-12">
    <div class="col-md-12">
        <div class="alert alert-info">
            <p><strong>{l s='Virtual Products with Combinations' mod='virtualproductscombinations'}</strong></p>
            <p>{l s='Configure downloadable files for each combination of this product.' mod='virtualproductscombinations'}</p>
            <p><em>{l s='Make sure to set the product type to "Product with combinations" in Step 1, create your combinations in Step 2, and then check "Does this product have an associated file?" to "Yes" in the Stock section.' mod='virtualproductscombinations'}</em></p>
        </div>
    </div>
    <div class="col-md-12 m-b-2">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="vpc_toggle_virtual" {if $is_virtual}checked{/if}>
            <label class="form-check-label" for="vpc_toggle_virtual">
                {l s='This product is virtual (no shipping)' mod='virtualproductscombinations'}
            </label>
        </div>
    </div>
    {* form_errors(form.step3.virtual_product) *}

    {foreach from=$virtual_products item=virtualProduct}
        <div class="col-md-1">&nbsp;</div>
        <div class="col-md-11"><h2>{$virtualProduct.combination_name|escape:'html':'UTF-8'}</h2></div>
        <div class="col-md-2">&nbsp;</div>
        <div class="col-md-10">
            <fieldset class="form-group">
                <label class="form-control-label">{l s='File' mod='virtualproductscombinations'}</label>
                <span class="help-box"
                      data-toggle="popover"
                      data-content="{l s='Upload a file from your computer (%s max.)' sprintf=$max_upload_size mod='virtualproductscombinations'}">
                </span>
                <div id="form_step3_virtual_product_file_input_{$virtualProduct.id_product_attribute|intval}" class="{if isset($virtualProduct.filename)}hide{else}show{/if}">
                    <input
                        type="file"
                        class="form_step3_virtual_product_file"
                        id="form_step3_virtual_product_file_{$virtualProduct.id_product_attribute|intval}"
                        name="form[step3][virtual_product][file]"
                        data-id-product-attribute="{$virtualProduct.id_product_attribute|intval}">
                </div>
                <div
                    id="form_step3_virtual_product_file_details_{$virtualProduct.id_product_attribute|intval}"
                    class="{if isset($virtualProduct.filename)}show{else}hide{/if} form_step3_virtual_product_file_details"
                    data-id-product-attribute="{$virtualProduct.id_product_attribute|intval}">
                    <a href="{if isset($virtualProduct.file_download_link)}{$virtualProduct.file_download_link}{/if}" class="btn btn-default btn-sm download">
                        {l s='Download file' mod='virtualproductscombinations'}
                    </a>
                    {if isset($virtualProduct.delete_link) && $virtualProduct.delete_link != ''}
                    <a href="{$virtualProduct.delete_link|escape:'html':'UTF-8'}" class="btn btn-danger btn-sm delete" data-id-product-attribute="{$virtualProduct.id_product_attribute|intval}">
                        {l s='Delete this file' mod='virtualproductscombinations'}
                    </a>
                    {/if}
                </div>
            </fieldset>
        </div>

        <div class="col-md-2">&nbsp;</div>
        <div class="col-md-5">
          <fieldset class="form-group">
            <label class="form-control-label">{l s='Filename' mod='virtualproductscombinations'}</label>
            <span class="help-box" data-toggle="popover"
                  data-content="{l s='The full filename with its extension (e.g. Book.pdf)' mod='virtualproductscombinations'}"></span>
            {* form_errors(form.step3.virtual_product.name) *}
            {* form_widget(form.step3.virtual_product.name) *}
            <input
                type="text"
                id="form_step3_virtual_product_name_{$virtualProduct.id_product_attribute|intval}"
                name="form[step3][virtual_product][name]"
                class="form-control"
                value="{$virtualProduct.name|escape:'html':'UTF-8'}"
                data-id-product-attribute="{$virtualProduct.id_product_attribute|intval}">
          </fieldset>
        </div>
        <div class="col-md-5">
          <fieldset class="form-group">
            <label class="form-control-label">{l s='Number of allowed downloads' mod='virtualproductscombinations'}</label>
            <span class="help-box" data-toggle="popover"
                  data-content="{l s='Number of downloads allowed per customer. Set to 0 for unlimited downloads.' mod='virtualproductscombinations'}"></span>
            {* form_errors(form.step3.virtual_product.nb_downloadable) *}
            {* form_widget(form.step3.virtual_product.nb_downloadable) *}
            <input type="text" id="form_step3_virtual_product_nb_downloadable_{$virtualProduct.id_product_attribute|intval}" name="form[step3][virtual_product][nb_downloadable]" class="form-control" value="{$virtualProduct.nb_downloadable|escape:'html':'UTF-8'}">
          </fieldset>
        </div>

        <div class="col-md-2">&nbsp;</div>
        <div class="col-md-5">
          <fieldset class="form-group">
            <label class="form-control-label">{l s='Expiration date' mod='virtualproductscombinations'}</label>
            <span class="help-box" data-toggle="popover"
                  data-content="{l s='If set, the file will not be downloadable after this date. Leave blank if you do not wish to attach an expiration date.' mod='virtualproductscombinations'}"></span>
            {* form_errors(form.step3.virtual_product.expiration_date) *}
            {* form_widget(form.step3.virtual_product.expiration_date) *}
            <div class="input-group datepicker">
                <input type="text" class="form-control" id="form_step3_virtual_product_expiration_date_{$virtualProduct.id_product_attribute|intval}" name="form[step3][virtual_product][expiration_date]" placeholder="YYYY-MM-DD" value="{$virtualProduct.expiration_date|escape:'html':'UTF-8'}"><div class="input-group-addon"><i class="material-icons">date_range</i></div>
            </div>
          </fieldset>
        </div>
        <div class="col-md-5">
          <fieldset class="form-group">
            <label class="form-control-label">{l s='Number of days' mod='virtualproductscombinations'}</label>
            <span class="help-box" data-toggle="popover"
                  data-content="{l s='Number of days this file can be accessed by customers. Set to zero for unlimited access.' mod='virtualproductscombinations'}"></span>
            {* form_errors(form.step3.virtual_product.nb_days) *}
            {* form_widget(form.step3.virtual_product.nb_days) *}
            <input type="text" id="form_step3_virtual_product_nb_days_{$virtualProduct.id_product_attribute|intval}" name="form[step3][virtual_product][nb_days]" class="form-control" value="{$virtualProduct.nb_days|escape:'html':'UTF-8'}">
          </fieldset>
        </div>

        <div class="col-md-2">&nbsp;</div>
        <div class="col-md-10">
            <input
                type="hidden"
                id="form_step3_virtual_product_save_link_{$virtualProduct.id_product_attribute|intval}"
                value="{$virtualProduct.save_link|escape:'html':'UTF-8'}" />

            <button
                type="button"
                name="form[step3][virtual_product][{$virtualProduct.id_product_attribute|intval}][save]"
                class="btn-primary btn m-b-1 form_step3_virtual_product_save"
                data-id-product-attribute="{$virtualProduct.id_product_attribute|intval}">{l s='Save' mod='virtualproductscombinations'}</button>
        </div>
    {/foreach}
</div>
