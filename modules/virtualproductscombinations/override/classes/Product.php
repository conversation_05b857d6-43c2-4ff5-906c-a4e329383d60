<?php
use PrestaShop\PrestaShop\Core\Domain\Product\ValueObject\ProductType;

class Product extends ProductCore
{
    public function add($autodate = true, $null_values = false)
    {
        $result = parent::add($autodate, $null_values);
        if ($result) {
            $this->normalizeProductTypeForCombinations();
        }
        return $result;
    }

    public function update($null_values = false)
    {
        $result = parent::update($null_values);
        if ($result) {
            $this->normalizeProductTypeForCombinations();
        }
        return $result;
    }

    /**
     * Keep product_type as combinations when product has combinations,
     * even if is_virtual = 1 (needed for modules and virtual logic).
     */
    protected function normalizeProductTypeForCombinations()
    {
        try {
            $hasCombinations = (bool) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue(
                'SELECT 1 FROM `' . _DB_PREFIX_ . 'product_attribute` WHERE id_product = ' . (int) $this->id . ' LIMIT 1'
            );

            if ($hasCombinations && $this->product_type === ProductType::TYPE_VIRTUAL) {
                Db::getInstance()->update(
                    'product',
                    array('product_type' => pSQL(ProductType::TYPE_COMBINATIONS)),
                    'id_product = ' . (int) $this->id
                );
                $this->product_type = ProductType::TYPE_COMBINATIONS;
            }
        } catch (Exception $e) {
            // best-effort, avoid breaking saves
        }
    }

    /**
     * Keep dynamic product type as combinations when combinations exist,
     * even if is_virtual = 1 (so combinations UI remains available in PS 8.2).
     */
    public function getDynamicProductType(): string
    {
        try {
            $hasCombinations = (bool) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue(
                'SELECT 1 FROM `' . _DB_PREFIX_ . 'product_attribute` WHERE id_product = ' . (int) $this->id . ' LIMIT 1'
            );
            if ($hasCombinations) {
                return ProductType::TYPE_COMBINATIONS;
            }
        } catch (Exception $e) {
            // Fallback to parent behavior
        }

        return parent::getDynamicProductType();
    }
}


