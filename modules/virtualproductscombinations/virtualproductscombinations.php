<?php
/**
* 2007-2018 PrestaShop
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/afl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Do not edit or add to this file if you wish to upgrade PrestaShop to newer
* versions in the future. If you wish to customize PrestaShop for your
* needs please refer to http://www.prestashop.com for more information.
*
*  <AUTHOR> SA <<EMAIL>>
*  @copyright 2007-2018 PrestaShop SA
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*  International Registered Trademark & Property of PrestaShop SA
*/

/**
 * Module virtualproductscombinations - Main file
 *
 * @category    Module / front_office_features
 * <AUTHOR> <<EMAIL>>
 * @translator  <PERSON>
 * @copyright   2013-2018 PrestaEdit
 * @link        http://www.prestaedit.com/
 * @since       File available since Release 1.0
*/

/* Security */
if (!defined('_PS_VERSION_')) {
    exit;
}

if (!class_exists('PrestaEditCoreClass')) {
    include_once _PS_ROOT_DIR_ . '/modules/virtualproductscombinations/PrestaEditCoreClass.php';
}

/* Checking compatibility with older PrestaShop and fixing it */
if (!defined('_MYSQL_ENGINE_')) {
    define('_MYSQL_ENGINE_', 'MyISAM');
}

class VirtualProductsCombinations extends PrestaEditCoreClass
{
    protected $legacyContainer;

    public function __construct()
    {
        $this->name = 'virtualproductscombinations';
        $this->tab = 'front_office_features';
        $this->version = '3.0.6';
        $this->author = 'PrestaEdit';
        $this->module_key = 'ac165d0253eb946ada642fc307b46096';
        $this->ps_versions_compliancy['min'] = '*******';
        $this->ps_versions_compliancy['max'] = _PS_VERSION_;

        $this->bootstrap = true;

        parent::__construct();

        if (Configuration::get('PS_DISABLE_OVERRIDES')) {
            $this->warning = sprintf($this->l('You need to set this option to false: %s'), $this->trans('Disable all overrides', array(), 'Admin.Advparameters.Feature'));
        }

        $this->displayName = $this->l('Virtual products with combinations');
        $this->description = $this->l('Add possibilities to use combinations with virtual products');

        if (defined('_PS_ADMIN_DIR_')) {
            global $kernel; // Note Team Validation - AdminProducts requiert une globale pour obtenir l'id_product courant
            if (!is_null($kernel)) {
                $this->legacyContainer = $kernel->getContainer();
            }
        }
    }

    public function install()
    {
        if (!parent::install() ||
            !$this->registerHook('actionAdminControllerSetMedia') ||
            !$this->registerHook('actionAdminProductsListingFieldsModifier') ||
            !$this->registerHook('displayBackOfficeHeader') ||
            !$this->registerHook('displayBackOfficeTop') ||
            !$this->registerHook('displayOverrideTemplate') ||
            !$this->registerHook('displayAdminProductsExtra') ||
            !$this->registerHook('actionObjectProductUpdateAfter') ||
            !$this->registerHook('actionObjectProductAddAfter')
        ) {
            return false;
        }

        // Install SQL - Custom installation to handle existing columns/keys
        $this->installDatabaseChanges();

        return true;
    }

    private function installDatabaseChanges()
    {
        $db = Db::getInstance();
        
        // Check if id_product_attribute column exists
       $columnExists = $db->executeS('SHOW COLUMNS FROM `'._DB_PREFIX_.'product_download` LIKE "id_product_attribute"');
        
        if (!$columnExists) {
            try {
                $db->execute('ALTER TABLE `'._DB_PREFIX_.'product_download` ADD COLUMN `id_product_attribute` INT(10) NOT NULL DEFAULT 0 AFTER `id_product`');
            } catch (Exception $e) {
                if (class_exists('Pres taShopLogger')) {
                    PrestaShopLogger::addLog('VirtualProductsCombinations: Error adding id_product_attribute column: ' . $e->getMessage(), 2);
                }
            }
        }
        
        // Try to drop the id_product key if it exists
        try {
            $keys = $db->executeS('SHOW KEYS FROM `'._DB_PREFIX_.'product_download` WHERE Key_name = "id_product"');
            if ($keys) {
                $db->execute('ALTER TABLE `'._DB_PREFIX_.'product_download` DROP KEY `id_product`');
            }
        } catch (Exception $e) {
            // Key might not exist, that's OK
            if (class_exists('PrestaShopLogger')) {
                PrestaShopLogger::addLog('VirtualProductsCombinations: Note - could not drop id_product key: ' . $e->getMessage(), 1);
            }
        }

        // Create module state table to persist forced virtual flag per product
        try {
            $db->execute('
                CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'vpc_forced_virtual` (
                    `id_product` INT(10) UNSIGNED NOT NULL,
                    `forced_virtual` TINYINT(1) NOT NULL DEFAULT 0,
                    PRIMARY KEY (`id_product`)
                ) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4;
            ');
        } catch (Exception $e) {
            if (class_exists('PrestaShopLogger')) {
                PrestaShopLogger::addLog('VirtualProductsCombinations: Error creating vpc_forced_virtual table: ' . $e->getMessage(), 2);
            }
        }
    }

    public function uninstall()
    {
        // Uninstall Module
        if (!parent::uninstall()) {
            return false;
        }

        // Uninstall SQL
        $sql = array();
        include(dirname(__FILE__).'/sql/uninstall.php');
        foreach ($sql as $s) {
            try {
                Db::getInstance()->execute($s);
            } catch (PrestaShopDatabaseException $e) {
                //
            }
        }

        // Drop module table
        try {
            Db::getInstance()->execute('DROP TABLE IF EXISTS `'._DB_PREFIX_.'vpc_forced_virtual`');
        } catch (Exception $e) {
            // ignore
        }

        return true;
    }

    public function hookActionAdminControllerSetMedia()
    {
        if (Module::isEnabled($this->name)) {
            // Check for both old and new controller names
            $controllerName = isset($this->context->controller->controller_name) ? $this->context->controller->controller_name : '';
            $isProductController = ($controllerName == 'AdminProducts' || $controllerName == 'AdminProductsController');
            
            if ($isProductController) {
                $idProduct = (int) $this->getCurrentProductIdFromRequest();

                if ((int)$idProduct) {
                    $this->context->controller->addJS($this->_path.'views/js/admin-products.js');
                }
            }
        }
    }

    public function hookDisplayBackOfficeHeader()
    {
        if (Module::isEnabled($this->name)) {
            // Check for both old and new controller names
            $controllerName = isset($this->context->controller->controller_name) ? $this->context->controller->controller_name : '';
            $isProductController = ($controllerName == 'AdminProducts' || $controllerName == 'AdminProductsController');
            
            if ($isProductController) {
                $idProduct = (int) $this->getCurrentProductIdFromRequest();

                if ((int) $idProduct) {
                    $vpcDisplayAdminProductsExtra = $this->getVirtualProductsForm((int) $idProduct);

                    if ($vpcDisplayAdminProductsExtra != '') {
                        Media::addJsDef(
                            array(
                                'vpc_display_admin_products_extra' => $vpcDisplayAdminProductsExtra,
                            )
                        );
                    }

                    Media::addJsDef(
                        array(
                            'vpc_configuration_link' => Context::getContext()->link->getAdminLink('AdminModules').'&configure='.$this->name.'&getVirtualProductsForm=1&id_product='.(int) $idProduct,
                            'vpc_toggle_virtual_link' => Context::getContext()->link->getAdminLink('AdminModules').'&configure='.$this->name.'&toggleProductVirtual=1&id_product='.(int) $idProduct.'&token='.(string)Tools::getAdminTokenLite('AdminModules'),
                        )
                    );
                }
            }
        }
    }

    protected function getVirtualProductsForm($idProduct)
    {
        $html = '';

        $legacyContext = $this->legacyContainer->get('prestashop.adapter.legacy.context');

        $virtualProductsData = $this->getVirtualProductsData((int) $idProduct, (int) $this->context->language->id, $legacyContext);


        if ($virtualProductsData['continue']) {
            $isVirtual = (bool) Db::getInstance()->getValue('SELECT IFNULL(v.forced_virtual, p.is_virtual) FROM `'._DB_PREFIX_.'product` p LEFT JOIN `'._DB_PREFIX_.'vpc_forced_virtual` v ON (v.id_product=p.id_product) WHERE p.id_product='.(int)$idProduct);
            $this->context->smarty->assign(
                array(
                    'idProduct' => (int) $idProduct,
                    'virtual_products' => $virtualProductsData['files'],
                    'max_upload_size' => Tools::formatBytes(Symfony\Component\HttpFoundation\File\UploadedFile::getMaxFilesize()),
                    'is_virtual' => $isVirtual,
                )
            );

            $html = $this->context->smarty->fetch(_PS_MODULE_DIR_ . $this->name . '/views/templates/admin/displayAdminProductsExtra.tpl');
        }

        return $html;
    }

    protected function getVirtualProductsData($idProduct, $idLang, $legacyContext)
    {
        // Force virtual product feature
        Configuration::updateGlobalValue('PS_VIRTUAL_PROD_FEATURE_ACTIVE', '1');

        $productAttributesIds = Product::getProductAttributesIds((int) $idProduct);
        if (is_array($productAttributesIds) && count($productAttributesIds)) {
            $virtualProductsData = array(
                'continue' => true,
                'files' => array()
            );
            foreach ($productAttributesIds as $productAttributeId) {
                $combinationName = $this->getProductAttributes((int) $productAttributeId['id_product_attribute'], (int) $idLang);

                $idProductDownload = ProductDownload::getIdFromCombination((int) $idProduct, (int) $productAttributeId['id_product_attribute'], false);
                if ($idProductDownload) {
                    $download = new ProductDownload((int) $idProductDownload);
                    $dateValue = $download->date_expiration == '0000-00-00 00:00:00' ? '' : date('Y-m-d', strtotime($download->date_expiration));

                    $virtualProductData = array(
                        'id_product_attribute' => (int) $productAttributeId['id_product_attribute'],
                        'combination_name' => $combinationName,
                        'is_virtual_file' => $download->active,
                        'name' => $download->display_filename,
                        'nb_downloadable' => $download->nb_downloadable,
                        'expiration_date' => $dateValue,
                        'nb_days' => $download->nb_days_accessible,
                        'save_link' => Context::getContext()->link->getAdminLink('AdminModules').'&configure='.$this->name.'&saveVirtualProductFile=1&id_product_attribute='.(int) $productAttributeId['id_product_attribute'].'&id_product='.(int) $idProduct,
                        'delete_link' => Context::getContext()->link->getAdminLink('AdminModules').'&configure='.$this->name.'&deleteVirtualProductFile=1&id_product_download='.(int) $idProductDownload.'&id_product='.(int) $idProduct,
                    );

                    if ($download->filename) {
                        $virtualProductData['filename'] = $download->filename;
                        try {
                            $adminDir = basename(_PS_ADMIN_DIR_);
                            $baseLink = Context::getContext()->link->getBaseLink();
                            $textLink = $download->getTextLink(true);
                            $virtualProductData['file_download_link'] = $baseLink . $adminDir . '/' . $textLink;
                        } catch (Exception $e) {
                            // Fallback if link generation fails
                            $virtualProductData['file_download_link'] = '';
                        }
                    } else {
                        $virtualProductData['file_download_link'] = '';
                    }
                } else {
                    $virtualProductData = array(
                        'id_product_attribute' => (int) $productAttributeId['id_product_attribute'],
                        'combination_name' => $combinationName,
                        'is_virtual_file' => false,
                        'name' => '',
                        'nb_downloadable' => 0,
                        'expiration_date' => '',
                        'nb_days' => 0,
                        'save_link' => Context::getContext()->link->getAdminLink('AdminModules').'&configure='.$this->name.'&saveVirtualProductFile=1&id_product_attribute='.(int) $productAttributeId['id_product_attribute'].'&id_product='.(int) $idProduct,
                        'delete_link' => '', // Empty delete link when no file exists
                        'file_download_link' => '', // Empty download link when no file exists
                    );
                }

                $virtualProductsData['files'][] = $virtualProductData;
            }

            return $virtualProductsData;
        } else {
            // No combinations
            return array(
                'continue' => false
            );
        }
    }

    /**
     * Generates a URL from the given parameters.
     *
     * @param string  $route      The name of the route
     * @param mixed   $parameters An array of parameters
     * @param Boolean $absolute   Whether to generate an absolute URL
     *
     * @return string The generated URL
     */
    protected function generateUrl($route, $parameters = array(), $absolute = false)
    {
        return $this->legacyContainer->get('router')->generate($route, $parameters, $absolute);
    }

    /**
     * Retrieves the current product id from the Sf request object
     * Compatible with PS 1.7 and PS 8
     *
     * @return int|null
     */
    protected function getCurrentProductIdFromRequest()
    {
        try {
            // Try multiple methods to get the product ID
            
            // Method 1: Try Tools::getValue for traditional GET/POST parameters
            $idProduct = (int)Tools::getValue('id_product');
            if ($idProduct > 0) {
                return $idProduct;
            }
            
            $idProduct = (int)Tools::getValue('id');
            if ($idProduct > 0) {
                return $idProduct;
            }
            
            // Method 2: Try Symfony request (for PS 1.7+)
            global $kernel;
            if ($kernel && $kernel->getContainer()) {
                if (version_compare(_PS_VERSION_, '*******', '>=')) {
                    $request = $kernel->getContainer()->get('request_stack')->getCurrentRequest();
                } else {
                    $request = $kernel->getContainer()->get('request');
                }
                
                if ($request) {
                    // Try different parameter names used in different PS versions
                    $currentIdProduct = $request->get('id');
                    if ($currentIdProduct) {
                        return (int)$currentIdProduct;
                    }
                    
                    $currentIdProduct = $request->get('id_product');
                    if ($currentIdProduct) {
                        return (int)$currentIdProduct;
                    }
                    
                    // For PS 8, try route parameters
                    if (method_exists($request, 'attributes') && $request->attributes) {
                        $routeParams = $request->attributes->get('_route_params', []);
                        if (isset($routeParams['id'])) {
                            return (int)$routeParams['id'];
                        }
                        if (isset($routeParams['id_product'])) {
                            return (int)$routeParams['id_product'];
                        }
                    }
                }
            }
            
            // Method 3: Try to get from URL path (fallback)
            if (isset($_SERVER['REQUEST_URI'])) {
                $uri = $_SERVER['REQUEST_URI'];
                // Look for patterns like /products/123/edit or /products/123
                if (preg_match('/\/products\/(\d+)/', $uri, $matches)) {
                    return (int)$matches[1];
                }
                // Look for id parameter in query string
                if (preg_match('/[?&]id=(\d+)/', $uri, $matches)) {
                    return (int)$matches[1];
                }
                if (preg_match('/[?&]id_product=(\d+)/', $uri, $matches)) {
                    return (int)$matches[1];
                }
            }
            
        } catch (Exception $e) {
            // Prevent any kind of error from blocking other modules
        }

        return null;
    }

    /**
     * Process Ajax Form to create/update virtual product
     *
     * @param int $idProduct
     * @param int $idProductAttribute
     * @param array $data
     *
     * @return string
     */
    public function processSaveVirtualProductFile($idProduct, $idProductAttribute)
    {
        // Always use direct processing for better compatibility
        return $this->processSaveVirtualProductFileDirectly($idProduct, $idProductAttribute);
    }

    /**
     * Process save virtual product file directly without Symfony forms
     * Fallback for PrestaShop 8 compatibility
     */
    private function processSaveVirtualProductFileDirectly($idProduct, $idProductAttribute)
    {
        try {
            // Log the incoming request for debugging
            PrestaShopLogger::addLog(
                'VirtualProductsCombinations processSaveVirtualProductFileDirectly called with idProduct: ' . $idProduct . ', idProductAttribute: ' . $idProductAttribute,
                1,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );
            
            $response = new \Symfony\Component\HttpFoundation\JsonResponse();
            
            $product = new Product((int) $idProduct);
            if (!Validate::isLoadedObject($product)) {
                PrestaShopLogger::addLog('VirtualProductsCombinations: Product not found: ' . $idProduct, 3, null, 'VirtualProductsCombinations', null, true);
                $response->setStatusCode(400);
                $response->setData(['error' => 'Product not found']);
                return $response;
            }

            // Log the incoming data for debugging
            PrestaShopLogger::addLog(
                'VirtualProductsCombinations: $_POST data: ' . print_r($_POST, true),
                1,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );
            
            PrestaShopLogger::addLog(
                'VirtualProductsCombinations: $_FILES data: ' . print_r($_FILES, true),
                1,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );

            // Get form data directly from $_POST and $_FILES
            $data = array();
            
            // Handle file upload - use a simple file wrapper instead of Symfony UploadedFile
            if (isset($_FILES['product_virtual']) && isset($_FILES['product_virtual']['tmp_name']['file']) && $_FILES['product_virtual']['tmp_name']['file']) {
                // Create a simple file object that mimics UploadedFile
                $data['file'] = new class($_FILES['product_virtual']['tmp_name']['file'], $_FILES['product_virtual']['name']['file']) {
                    private $tmpName;
                    private $originalName;
                    
                    public function __construct($tmpName, $originalName) {
                        $this->tmpName = $tmpName;
                        $this->originalName = $originalName;
                    }
                    
                    public function move($directory, $name = null) {
                        $targetPath = $directory . ($name ?: $this->originalName);
                        return move_uploaded_file($this->tmpName, $targetPath);
                    }
                    
                    public function getClientOriginalName() {
                        return $this->originalName;
                    }
                };
                PrestaShopLogger::addLog('VirtualProductsCombinations: File uploaded: ' . $_FILES['product_virtual']['name']['file'], 1, null, 'VirtualProductsCombinations', null, true);
            } else {
                PrestaShopLogger::addLog('VirtualProductsCombinations: No file uploaded', 1, null, 'VirtualProductsCombinations', null, true);
            }
            
            // Get other form data
            $productVirtual = Tools::getValue('product_virtual', array());
            $data['is_virtual_file'] = isset($productVirtual['is_virtual_file']) ? $productVirtual['is_virtual_file'] : '1';
            $data['name'] = isset($productVirtual['name']) ? $productVirtual['name'] : '';
            $data['nb_downloadable'] = isset($productVirtual['nb_downloadable']) ? (int)$productVirtual['nb_downloadable'] : 0;
            $data['expiration_date'] = isset($productVirtual['expiration_date']) ? $productVirtual['expiration_date'] : '';
            $data['nb_days'] = isset($productVirtual['nb_days']) ? (int)$productVirtual['nb_days'] : 0;

            PrestaShopLogger::addLog(
                'VirtualProductsCombinations: Processed data: ' . print_r($data, true),
                1,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );

            $res = $this->updateDownloadProduct($idProduct, $idProductAttribute, $data);
            
            PrestaShopLogger::addLog(
                'VirtualProductsCombinations: updateDownloadProduct result: ' . print_r($res, true),
                1,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );
            
            // Generate download link
            if ($res->filename) {
                try {
                    $adminDir = basename(_PS_ADMIN_DIR_);
                    $baseLink = Context::getContext()->link->getBaseLink();
                    $textLink = $res->getTextLink(true);
                    $res->file_download_link = $baseLink . $adminDir . '/' . $textLink;
                } catch (Exception $e) {
                    // Fallback if link generation fails
                    $res->file_download_link = '';
                }
            } else {
                $res->file_download_link = '';
            }

            // Update is_virtual via direct SQL to avoid triggering full Product::save()
            // which can validate unrelated associations (e.g., suppliers) and fail.
            Db::getInstance()->update('product', array('is_virtual' => 1), 'id_product='.(int)$product->id);

            // Keep product type as "standard" if combinations exist (PS 8.2 UI needs this)
            $hasCombinations = (bool) Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'product_attribute` WHERE id_product='.(int)$product->id);
            if ($hasCombinations) {
                Db::getInstance()->update('product', array('product_type' => pSQL('combinations')), 'id_product='.(int)$product->id);
            }

            // Convert the ProductDownload object to array for JSON response
            $responseData = array(
                'id' => $res->id,
                'filename' => $res->filename,
                'display_filename' => $res->display_filename,
                'file_download_link' => $res->file_download_link,
                'nb_downloadable' => $res->nb_downloadable,
                'nb_days_accessible' => $res->nb_days_accessible,
                'date_expiration' => $res->date_expiration,
                'active' => $res->active
            );

            $response->setData($responseData);
            return $response;
            
        } catch (Exception $e) {
            PrestaShopLogger::addLog(
                'VirtualProductsCombinations processSaveVirtualProductFileDirectly error: ' . $e->getMessage() . ' - Stack trace: ' . $e->getTraceAsString(),
                3,
                null,
                'VirtualProductsCombinations',
                null,
                true
            );
            
            $response = new \Symfony\Component\HttpFoundation\JsonResponse();
            $response->setStatusCode(500);
            $response->setData(['error' => 'Internal error: ' . $e->getMessage()]);
            return $response;
        }
    }

    /**
     * Update product download
     *
     * @param int $idProduct
     * @param int $idProductAttribute
     * @param array $data
     *
     * @return bool
     */
    public function updateDownloadProduct($idProduct, $idProductAttribute, $data)
    {
        $idProductDownload = ProductDownload::getIdFromCombination((int) $idProduct, (int) $idProductAttribute, false);
        $download = new ProductDownload($idProductDownload ? $idProductDownload : null);

        if ((int) $data['is_virtual_file'] == 1) {
            $fileName = null;
            $file = $data['file'];

            if (!empty($file)) {
                $fileName = ProductDownload::getNewFilename();
                $file->move(_PS_DOWNLOAD_DIR_, $fileName);
            }

            $download->id_product = (int) $idProduct;
            $download->id_product_attribute = (int) $idProductAttribute;
            $download->display_filename = $data['name'];
            $download->filename = $fileName ? $fileName : $download->filename;
            $download->date_add = date('Y-m-d H:i:s');
            $download->date_expiration = $data['expiration_date'] ? $data['expiration_date'].' 23:59:59' : '';
            $download->nb_days_accessible = (int) $data['nb_days'];
            $download->nb_downloadable = (int) $data['nb_downloadable'];
            $download->active = 1;
            $download->is_shareable = 0;

            if (!$idProductDownload) {
                $download->save();
            } else {
                $download->update();
            }
        } else {
            if (!empty($idProductDownload)) {
                $download->date_expiration = date('Y-m-d H:i:s', time() - 1);
                $download->active = 0;
                $download->update();
            }
        }

        return $download;
    }

     /**
     * Delete file from a virtual product
     *
     * @param object $product
     */
    public function processDeleteVirtualProductFile($idProductDownload)
    {
        try {
            PrestaShopLogger::addLog('VirtualProductsCombinations: Deleting file for download ID: ' . $idProductDownload, 1, null, 'VirtualProductsCombinations', null, true);
            
            $download = new ProductDownload((int) $idProductDownload);

            if ($download && !empty($download->filename)) {
                $filePath = _PS_DOWNLOAD_DIR_ . $download->filename;
                if (file_exists($filePath)) {
                    unlink($filePath);
                    PrestaShopLogger::addLog('VirtualProductsCombinations: File deleted: ' . $filePath, 1, null, 'VirtualProductsCombinations', null, true);
                }
                
                // Clear the filename in database
                Db::getInstance()->execute('UPDATE `'._DB_PREFIX_.'product_download` SET filename = "", active = 0 WHERE `id_product_download` = '.(int)$download->id);
                PrestaShopLogger::addLog('VirtualProductsCombinations: Database updated for download ID: ' . $download->id, 1, null, 'VirtualProductsCombinations', null, true);
            } else {
                PrestaShopLogger::addLog('VirtualProductsCombinations: No file to delete for download ID: ' . $idProductDownload, 1, null, 'VirtualProductsCombinations', null, true);
            }
            
            return true;
        } catch (Exception $e) {
            PrestaShopLogger::addLog('VirtualProductsCombinations: Error deleting file: ' . $e->getMessage(), 3, null, 'VirtualProductsCombinations', null, true);
            return false;
        }
    }

    protected function getProductAttributes($idProductAttribute, $idLang = null)
    {
        if ($idLang == null) {
            $idLang = (int) Context::getContext()->language->id;
        }

        $result = Db::getInstance()->executeS(
            '
            SELECT pac.`id_product_attribute`, agl.`public_name` AS public_group_name, al.`name` AS attribute_name
            FROM `'._DB_PREFIX_.'product_attribute_combination` pac
            LEFT JOIN `'._DB_PREFIX_.'attribute` a ON a.`id_attribute` = pac.`id_attribute`
            LEFT JOIN `'._DB_PREFIX_.'attribute_group` ag ON ag.`id_attribute_group` = a.`id_attribute_group`
            LEFT JOIN `'._DB_PREFIX_.'attribute_lang` al ON (
                a.`id_attribute` = al.`id_attribute`
                AND al.`id_lang` = '.(int) $idLang.'
            )
            LEFT JOIN `'._DB_PREFIX_.'attribute_group_lang` agl ON (
                ag.`id_attribute_group` = agl.`id_attribute_group`
                AND agl.`id_lang` = '.(int) $idLang.'
            )
            WHERE pac.`id_product_attribute` = '.(int) $idProductAttribute.'
            ORDER BY ag.`position` ASC, a.`position` ASC'
        );

        $attributes_lists = '';
        // Construct attributes list
        foreach ($result as $row) {
            $attributes_lists .= $row['public_group_name'].' : '.$row['attribute_name'].', ';
        }
        // Remove extra dots
        $attributes_lists = rtrim($attributes_lists, ', ');

        return $attributes_lists;
    }

    public function hookDisplayBackOfficeTop()
    {
        if (Configuration::get('PS_DISABLE_OVERRIDES')) {
            return '<div class="col-lg-4">'.$this->displayWarning('<b>'.$this->displayName.'</b><br />'.sprintf($this->l('You need to set this option to false: %s'), $this->trans('Disable all overrides', array(), 'Admin.Advparameters.Feature'))).'</div>';
        }
    }

    public function hookActionAdminProductsListingFieldsModifier($params)
    {
        $alreadyDone = false;
        if (isset($params['sql_where']) && count($params['sql_where'])) {
            foreach ($params['sql_where'] as $where) {
                if ($where == '1 GROUP BY `id_product`') {
                    $alreadyDone = true;
                }
            }
        }
        if (!$alreadyDone) {
            $params['sql_where'] = array('1 GROUP BY `id_product`');
        }
    }

    protected function processGetContent()
    {
        // Log all requests to this method for debugging
        PrestaShopLogger::addLog(
            'VirtualProductsCombinations processGetContent called with GET: ' . print_r($_GET, true) . ' POST: ' . print_r($_POST, true),
            1,
            null,
            'VirtualProductsCombinations',
            null,
            true
        );
        
        $content = '';

        if (Configuration::get('PS_DISABLE_OVERRIDES')) {
            $content .= $this->displayWarning(sprintf($this->l('You need to set this option to false: %s'), $this->trans('Disable all overrides', array(), 'Admin.Advparameters.Feature')));
        }

        if (Tools::isSubmit('deleteVirtualProductFile')) {
            PrestaShopLogger::addLog('VirtualProductsCombinations: Processing deleteVirtualProductFile', 1, null, 'VirtualProductsCombinations', null, true);
            $idProduct = (int) Tools::getValue('id_product', 0);
            $idProductDownload = (int) Tools::getValue('id_product_download', 0);
            $this->processDeleteVirtualProductFile((int) $idProductDownload);

            Tools::redirectAdmin($this->generateUrl('admin_product_form', array('id' => (int) $idProduct)).'#tab-step3');
        }

        if (Tools::isSubmit('saveVirtualProductFile')) {
            PrestaShopLogger::addLog('VirtualProductsCombinations: Processing saveVirtualProductFile', 1, null, 'VirtualProductsCombinations', null, true);
            $idProduct = (int) Tools::getValue('id_product', 0);
            $idProductAttribute = (int) Tools::getValue('id_product_attribute', 0);

            PrestaShopLogger::addLog('VirtualProductsCombinations: idProduct=' . $idProduct . ', idProductAttribute=' . $idProductAttribute, 1, null, 'VirtualProductsCombinations', null, true);

            try {
                $response = $this->processSaveVirtualProductFile((int) $idProduct, (int) $idProductAttribute);
                PrestaShopLogger::addLog('VirtualProductsCombinations: Response created successfully', 1, null, 'VirtualProductsCombinations', null, true);
                $response->send();
                die;
            } catch (Exception $e) {
                PrestaShopLogger::addLog('VirtualProductsCombinations: Error in processGetContent: ' . $e->getMessage() . ' - Trace: ' . $e->getTraceAsString(), 3, null, 'VirtualProductsCombinations', null, true);
                
                // Send error response
                $response = new \Symfony\Component\HttpFoundation\JsonResponse();
                $response->setStatusCode(500);
                $response->setData(['error' => 'Error: ' . $e->getMessage()]);
                $response->send();
                die;
            }
        }

        if (Tools::isSubmit('getVirtualProductsForm')) {
            PrestaShopLogger::addLog('VirtualProductsCombinations: Processing getVirtualProductsForm', 1, null, 'VirtualProductsCombinations', null, true);
            $response = new \Symfony\Component\HttpFoundation\JsonResponse();

            $idProduct = (int) Tools::getValue('id_product', 0);

            $data = array('content' => $this->getVirtualProductsForm((int) $idProduct));

            $response->setData($data);
            $response->send();
            die;
        }

        if (Tools::isSubmit('toggleProductVirtual')) {
            $idProduct = (int) Tools::getValue('id_product', 0);
            $state = (int) Tools::getValue('state', 1);
            if ($idProduct > 0) {
                // Persist the admin toggle in our module table
                Db::getInstance()->execute('REPLACE INTO `'._DB_PREFIX_.'vpc_forced_virtual` (id_product, forced_virtual) VALUES ('.(int)$idProduct.', '.(int)$state.')');

                // Update core product flags accordingly (so FE/BO reflect the state)
                Db::getInstance()->update('product', array('is_virtual' => (int)$state), 'id_product='.(int)$idProduct);
                $hasCombinations = (bool) Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'product_attribute` WHERE id_product='.(int)$idProduct);
                if ($hasCombinations) {
                    Db::getInstance()->update('product', array('product_type' => pSQL('combinations')), 'id_product='.(int)$idProduct);
                }
            }
            $response = new \Symfony\Component\HttpFoundation\JsonResponse();
            $response->setData(['ok' => true]);
            $response->send();
            die;
        }

        PrestaShopLogger::addLog('VirtualProductsCombinations: processGetContent completed normally', 1, null, 'VirtualProductsCombinations', null, true);
        return $content;
    }

    public function hookDisplayOverrideTemplate($params)
    {
        if (isset($params['controller']) && $params['controller'] instanceof OrderDetailController) {
            $smarty = Context::getContext()->smarty;
            $orderVar = null;
            if (method_exists($smarty, 'getTemplateVars')) {
                $orderVar = $smarty->getTemplateVars('order');
            } elseif (method_exists($smarty, 'get_template_vars')) {
                $orderVar = $smarty->get_template_vars('order');
            }

            if ($orderVar !== null) {
                $order = new Order((int) $orderVar['details']['id']);
                foreach ($orderVar['products'] as &$orderProduct) {
                    if (isset($orderProduct['download_link'])) {
                        if ((int)$orderProduct['product_attribute_id'] > 0) {
                            $id_product_download = ProductDownload::getIdFromCombination((int) $orderProduct['product_id'], (int) $orderProduct['product_attribute_id']);
                        } else {
                            $id_product_download = ProductDownload::getIdFromIdProduct((int) $orderProduct['product_id']);
                        }
                        $product_download = new ProductDownload($id_product_download);
                        if ($product_download->display_filename != '') {
                            $orderProduct['download_link'] = $product_download->getTextLink(false, $orderProduct['download_hash'])
                                . '&id_order=' . (int)$order->id
                                . '&secure_key=' . $order->secure_key;
                        }
                    }
                }

                $this->context->smarty->assign('order', $orderVar);
            }
        }
    }

    public function hookDisplayAdminProductsExtra($params)
    {
        if (isset($params['id_product'])) {
            $idProduct = (int) $params['id_product'];
            return $this->getVirtualProductsForm($idProduct);
        }
        return '';
    }

    /**
     * Ensure persistence: after core product save, if combinations exist and admin set our toggle (or
     * there are active combination downloads), enforce is_virtual=1 and product_type=standard.
     */
    public function hookActionObjectProductUpdateAfter($params)
    {
        $this->enforceVirtualFlagAndType($params);
    }

    public function hookActionObjectProductAddAfter($params)
    {
        $this->enforceVirtualFlagAndType($params);
    }

    protected function enforceVirtualFlagAndType($params)
    {
        if (empty($params['object']) || !($params['object'] instanceof Product)) {
            return;
        }
        $product = $params['object'];
        $idProduct = (int) $product->id;
        if ($idProduct <= 0) {
            return;
        }

        // Determine desired virtual state: persisted admin toggle OR presence of any combination download rows
        $forcedVirtual = (bool) Db::getInstance()->getValue('SELECT forced_virtual FROM `'._DB_PREFIX_.'vpc_forced_virtual` WHERE id_product='.(int)$idProduct);
        $hasCombinationDownloads = (bool) Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'product_download` WHERE id_product='.(int)$idProduct.' AND filename<>"" AND active=1');
        $shouldBeVirtual = $forcedVirtual || $hasCombinationDownloads;

        // Persist is_virtual as needed
        Db::getInstance()->update('product', array('is_virtual' => (int)$shouldBeVirtual), 'id_product='.(int)$idProduct);

        // Keep product type standard if combinations exist
        $hasCombinations = (bool) Db::getInstance()->getValue('SELECT COUNT(*) FROM `'._DB_PREFIX_.'product_attribute` WHERE id_product='.(int)$idProduct);
        if ($hasCombinations) {
            Db::getInstance()->update('product', array('product_type' => pSQL('combinations')), 'id_product='.(int)$idProduct);
        }
    }
}
