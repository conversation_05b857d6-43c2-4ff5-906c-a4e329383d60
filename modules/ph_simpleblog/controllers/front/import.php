<?php

class Ph_SimpleBlogImportModuleFrontController extends ModuleFrontController
{
    public function init()
    {
        // Increase script-side time limits for long-running imports (best-effort)
        if (function_exists('ignore_user_abort')) { @ignore_user_abort(true); }
        if (function_exists('set_time_limit')) { @set_time_limit(120); }
        @ini_set('max_execution_time', '120');
        @ini_set('default_socket_timeout', '120');
        // echo 'Comment line 7 before running the script.';exit;

        $action = Tools::getValue('action');

        if ($action === 'import_product_vat') {
            header('Content-Type: application/json; charset=utf-8');
            $url = Tools::getValue('url');
            $result = $this->importProductVat($url ?: null);
            echo json_encode($result);
            exit;
        }

        if ($action === 'import_product_features') {
            header('Content-Type: application/json; charset=utf-8');
            $url = Tools::getValue('url');
            $result = $this->importProductFeatures($url ?: null);
            echo json_encode($result);
            exit;
        }

        if ($action === 'import_product_stock') {
            header('Content-Type: application/json; charset=utf-8');
            $url = Tools::getValue('url');
            $result = $this->importProductStock($url ?: null);
            echo json_encode($result);
            exit;
        }

        if ($action === 'import_modules') {
            header('Content-Type: application/json; charset=utf-8');
            $this->importModules();
            echo json_encode(['ok' => true]);
            exit;
        }

        if ($action === 'import_products') {
            header('Content-Type: application/json; charset=utf-8');
            $this->importProducts();
            echo json_encode(['ok' => true]);
            exit;
        }

        if ($action === 'import_addresses') {
            header('Content-Type: application/json; charset=utf-8');
            $this->importAddresses();
            echo json_encode(['ok' => true]);
            exit;
        }

        if ($action === 'import_tables') {
            header('Content-Type: application/json; charset=utf-8');
            $this->importTables();
            echo json_encode(['ok' => true]);
            exit;
        }

        if ($action === 'products_assign_sellers') {
            header('Content-Type: application/json; charset=utf-8');
            $result = $this->productsAssignSelllers();
            echo json_encode($result);
            exit;
        }

        if ($action === 'orders_assign_sellers') {
            header('Content-Type: application/json; charset=utf-8');
            $result = $this->ordersAssignSellers();
            echo json_encode($result);
            exit;
        }

        if ($action === 'product_regenerate_images') {
            header('Content-Type: application/json; charset=utf-8');
            $result = $this->productRegenerateImages();
            echo json_encode($result);
            exit;
        }

        if ($action === 'sync_combinations') {
            header('Content-Type: application/json; charset=utf-8');
            $url = Tools::getValue('url');
            $result = $this->syncCombinations($url ?: null);
            echo json_encode($result);
            exit;
        }

        if ($action === 'assign_shipping_type') {
            header('Content-Type: application/json; charset=utf-8');
            $result = $this->assignShippingType();
            echo json_encode($result);
            exit;
        }

        if ($action === 'audit_product_combinations') {
            $idProduct = (int)Tools::getValue('id_product');
            $result = $this->auditProductCombinations($idProduct);
            if (Tools::getValue('format') === 'html') {
                header('Content-Type: text/html; charset=utf-8');
                echo $this->renderAuditHtml($result);
                exit;
            }
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode($result);
            exit;
        }
    }

    private function importModules()
    {
        $data_raw = json_decode(file_get_contents('https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export'), true);
        $my_modules_raw = $this->getModulesForExport();

        // Create maps for easier lookup, keyed by module name.
        $data = array_column($data_raw, null, 'name');
        $my_modules = array_column($my_modules_raw, null, 'name');

        $missing_modules = [];
        // Group missing modules by their live status
        $missing_modules_1_1 = []; // live: installed=1, enabled=1 (but missing locally)
        $missing_modules_1_0 = []; // live: installed=1, enabled=0 (but missing locally)
        $missing_modules_0_0 = []; // live: installed=0, enabled=0 (but missing locally)

        $status_1_1_1_0 = []; // data: installed=1, enabled=1, my_modules: installed=1, enabled=0
        $status_1_1_0_0 = []; // data: installed=1, enabled=1, my_modules: installed=0, enabled=0
        $status_1_0_x_x = []; // data: installed=1, enabled=0, my_modules: whatever
        $status_0_0_x_x = []; // data: installed=0, enabled=0, my_modules: whatever
        $status_other = [];   // any other status combinations

        $all_different = array();
        // Check modules from the remote source against local modules.
        foreach ($data as $name => $data_module) {
            if (!isset($my_modules[$name])) {
                // Module from source is missing locally.
                $module_data = [
                    'live' => $data_module,
                    'nexloc' => null,
                ];

                $missing_modules[$name] = $module_data;
                $all_different[$name] = $module_data;

                // Group missing modules by their live status
                $live_installed = $data_module['installed'];
                $live_enabled = $data_module['enabled'];

                if ($live_installed == 1 && $live_enabled == 1) {
                    $missing_modules_1_1[$name] = $module_data;
                } elseif ($live_installed == 1 && $live_enabled == 0) {
                    $missing_modules_1_0[$name] = $module_data;
                } elseif ($live_installed == 0 && $live_enabled == 0) {
                    $missing_modules_0_0[$name] = $module_data;
                }
            } elseif ($data_module['installed'] != $my_modules[$name]['installed'] || $data_module['enabled'] != $my_modules[$name]['enabled']) {
                // Module exists, but its installation or enabled status is different.
                $module_pair = [
                    'live' => $data_module,
                    'nexloc' => $my_modules[$name],
                ];

                $all_different[$name] = $module_pair;

                $data_installed = $data_module['installed'];
                $data_enabled = $data_module['enabled'];
                $my_installed = $my_modules[$name]['installed'];
                $my_enabled = $my_modules[$name]['enabled'];

                if ($data_installed == 1 && $data_enabled == 1 && $my_installed == 1 && $my_enabled == 0) {
                    $status_1_1_1_0[$name] = $module_pair;
                } elseif ($data_installed == 1 && $data_enabled == 1 && $my_installed == 0 && $my_enabled == 0) {
                    $status_1_1_0_0[$name] = $module_pair;
                } elseif ($data_installed == 1 && $data_enabled == 0) {
                    $status_1_0_x_x[$name] = $module_pair;
                } elseif ($data_installed == 0 && $data_enabled == 0) {
                    $status_0_0_x_x[$name] = $module_pair;
                } else {
                    $status_other[$name] = $module_pair;
                }
            }
        }

        dump([
            'missing_modules' => $missing_modules,
            'missing_modules_1_1' => $missing_modules_1_1,
            'missing_modules_1_0' => $missing_modules_1_0,
            'missing_modules_0_0' => $missing_modules_0_0,
            'status_1_1_1_0' => $status_1_1_1_0,
            'status_1_1_0_0' => $status_1_1_0_0,
            'status_1_0_x_x' => $status_1_0_x_x,
            'status_0_0_x_x' => $status_0_0_x_x,
            'status_other' => $status_other,
            'all_different' => $all_different,
            'all_modules' => $data,
        ]);
        exit();
    }

    private function getModulesForExport()
    {
        $modules_on_disk = Module::getModulesOnDisk(true);
        $modules_formatted = [];

        foreach ($modules_on_disk as $module) {
            $modules_formatted[] = [
                'name' => $module->name,
                'display_name' => $module->displayName,
                'installed' => (int)Module::isInstalled($module->name),
                'enabled' => (int)Module::isEnabled($module->name),
            ];
        }

        return $modules_formatted;
    }

    /**
     * Import product Tax Rules Group selection.
     *
     * Usage:
     * - $dataOrUrl can be an array of rows from exportProductVat or a URL returning that JSON.
     * - If tax groups share the same ids across shops, ids are used directly.
     * - Otherwise we try to map by your manual group id mapping, then by tax rules group name.
     * - Updates product_shop when available, otherwise updates product.
     *
     * Row shape expected per entry:
     *   [
     *     'id_product' => int,
     *     'id_shop' => int|null,
     *     'tax_rules_group_id' => int,
     *     'tax_rules_group_name' => string|null
     *   ]
     *
     * @param string|array|null $dataOrUrl
     * @param int|null $targetShopId Optionally force apply to this shop id (multishop).
     * @param array<int,int> $groupIdMap Optional mapping from source group id => target group id
     * @return array Summary with counters
     */
    private function importProductVat($dataOrUrl = null, $targetShopId = null)
    {
        // Fixed target shop id for the new shop
        $targetShopId = 1;

        // Manual mapping: old shop group id => new shop group id
        $groupIdMap = [
            7 => 12,
            8 => 13,
            9 => 14,
            10 => 15,
        ];

        // Restrict import to specific product ids (if non-empty, import ONLY these ids)
        $import_products_id = [];
        $importProductsSet = [];
        foreach ($import_products_id as $pid) {
            $pidInt = (int) $pid;
            if ($pidInt > 0) {
                $importProductsSet[$pidInt] = true;
            }
        }

        // Resolve input data
        if ($dataOrUrl === null) {
            // Default: fetch from the same endpoint used by other import methods
            // Expect export controller with action=export_product_vat
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_product_vat';
        }

        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }

        if (!is_array($payload)) {
            return [
                'ok' => false,
                'reason' => 'Invalid payload',
            ];
        }

        $db = Db::getInstance();

        // Detect schema
        $hasProductShopTable = !empty($db->executeS('SHOW TABLES LIKE "' . pSQL(_DB_PREFIX_ . 'product_shop') . '"'));
        $hasProductShopTax = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product_shop` LIKE "id_tax_rules_group"'));
        $hasProductTax = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "id_tax_rules_group"'));

        // Build local name->id map for tax rules groups (for fallback mapping)
        $localGroups = $db->executeS('SELECT id_tax_rules_group, name FROM `' . _DB_PREFIX_ . 'tax_rules_group`');
        $nameToId = [];
        foreach ($localGroups as $g) {
            $nameToId[(string) $g['name']] = (int) $g['id_tax_rules_group'];
        }

        $updated = 0;
        $skippedMissing = 0;
        $skippedNoSchema = 0;

        foreach ($payload as $row) {
            if (!is_array($row) || !isset($row['id_product'])) {
                continue;
            }

            $idProduct = (int) $row['id_product'];

            // If a restriction list is provided, skip products not in the list
            if (!empty($importProductsSet) && !isset($importProductsSet[$idProduct])) {
                continue;
            }
            $srcGroupId = isset($row['tax_rules_group_id']) ? (int) $row['tax_rules_group_id'] : 0;
            $srcGroupName = isset($row['tax_rules_group_name']) ? (string) $row['tax_rules_group_name'] : null;

            // Find target group id with priority:
            // 1) explicit mapping ($groupIdMap)
            // 2) same id if exists locally
            // 3) by group name
            $targetGroupId = $srcGroupId;
            if ($srcGroupId > 0 && isset($groupIdMap[$srcGroupId])) {
                $targetGroupId = (int) $groupIdMap[$srcGroupId];
            }

            if ($targetGroupId > 0) {
                $exists = $db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'tax_rules_group` WHERE id_tax_rules_group = ' . (int) $targetGroupId);
                if (!$exists && $srcGroupName !== null && isset($nameToId[$srcGroupName])) {
                    $targetGroupId = $nameToId[$srcGroupName];
                }
            } elseif ($srcGroupName !== null && isset($nameToId[$srcGroupName])) {
                $targetGroupId = $nameToId[$srcGroupName];
            }

            if ($targetGroupId < 0) {
                $targetGroupId = 0;
            }

            // Determine shop to update when using product_shop
            $rowShopId = isset($row['id_shop']) ? (int) $row['id_shop'] : null;
            $applyShopId = $targetShopId !== null ? (int) $targetShopId : $rowShopId;

            // Apply update: prefer product_shop if available, but also update base product for safety
            if ($hasProductShopTable && $hasProductShopTax && $applyShopId !== null) {
                $updatedOkShop = $db->update(
                    'product_shop',
                    ['id_tax_rules_group' => (int) $targetGroupId],
                    'id_product = ' . (int) $idProduct . ' AND id_shop = ' . (int) $applyShopId
                );

                // If no row was updated, ensure a row exists then try again
                if (!$updatedOkShop) {
                    $rowExists = (bool) $db->getValue(
                        'SELECT 1 FROM `' . _DB_PREFIX_ . 'product_shop` WHERE id_product = ' . (int) $idProduct . ' AND id_shop = ' . (int) $applyShopId . ' LIMIT 1'
                    );
                    if (!$rowExists) {
                        // Create minimal association row; rely on DB defaults for other columns
                        $db->execute(
                            'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'product_shop` (id_product, id_shop, id_tax_rules_group) VALUES (' . (int) $idProduct . ', ' . (int) $applyShopId . ', ' . (int) $targetGroupId . ')'
                        );
                        // Re-try update to be explicit
                        $updatedOkShop = $db->update(
                            'product_shop',
                            ['id_tax_rules_group' => (int) $targetGroupId],
                            'id_product = ' . (int) $idProduct . ' AND id_shop = ' . (int) $applyShopId
                        );
                    }
                }

                $updatedOkBase = false;
                if ($hasProductTax) {
                    $updatedOkBase = $db->update(
                        'product',
                        ['id_tax_rules_group' => (int) $targetGroupId],
                        'id_product = ' . (int) $idProduct
                    );
                }

                if ($updatedOkShop || $updatedOkBase) {
                    $updated++;
                } else {
                    $skippedMissing++;
                }
            } elseif ($hasProductTax) {
                $updatedOk = $db->update(
                    'product',
                    ['id_tax_rules_group' => (int) $targetGroupId],
                    'id_product = ' . (int) $idProduct
                );
                if ($updatedOk) {
                    $updated++;
                } else {
                    $skippedMissing++;
                }
            } else {
                $skippedNoSchema++;
            }

            // Also update via Product object to ensure BO reflects changes immediately
            if (class_exists('Product') && class_exists('Validate')) {
                $productObj = new Product((int) $idProduct, false, null, (int) $targetShopId);
                if (Validate::isLoadedObject($productObj)) {
                    $productObj->id_tax_rules_group = (int) $targetGroupId;
                    $productObj->save();
                }
            }
        }

        // Clear cache so BO doesn't show stale values
        if (class_exists('Cache')) {
            Cache::clean('*');
        }

        return [
            'ok' => true,
            'updated' => $updated,
            'skipped_missing' => $skippedMissing,
            'skipped_no_schema' => $skippedNoSchema,
        ];
    }

    /**
     * Import product features from 1.7.8 export to 8.2 using raw SQL.
     * - Skips products not in $products_import when list provided.
     * - Compares existing features; updates only when different.
     * - Assumes feature and feature_value ids match between shops. If not, tries by names.
     *
     * Export row shape expected:
     *   {
     *     id_product, id_feature, id_feature_value, custom, feature: {iso=>name}, value: {iso=>value}
     *   }
     *
     * @param string|array|null $dataOrUrl
     * @return array{ok:bool,processed:int,unchanged:int,updated:int,created_values:int,missing_products:int,errors:int}
     */
    private function importProductFeatures($dataOrUrl = null)
    {
        $db = Db::getInstance();

        // Optional product filter. Fill with ids to restrict.
        $products_import = array();
        $productsFilter = [];
        foreach ($products_import as $pid) {
            $pid = (int)$pid;
            if ($pid > 0) {
                $productsFilter[$pid] = true;
            }
        }

        // Optional: when no filter is provided, optionally clear features for ALL products
        // that are absent from the payload. Toggle here:
        $clearAbsentAll = true; // set to true to clear features for all products absent from payload when no filter is provided

        // Load payload
        if ($dataOrUrl === null) {
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_product_features';
        }
        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }
        if (!is_array($payload)) {
            return [
                'ok' => false,
                'processed' => 0,
                'unchanged' => 0,
                'updated' => 0,
                'created_values' => 0,
                'missing_products' => 0,
                'errors' => 1,
            ];
        }

        // Active languages map iso->id_lang
        $langs = $db->executeS('SELECT id_lang, iso_code FROM `' . _DB_PREFIX_ . 'lang` WHERE active = 1');
        $isoToLang = [];
        foreach ($langs as $l) {
            $isoToLang[(string)$l['iso_code']] = (int)$l['id_lang'];
        }

        $processed = 0;
        $unchanged = 0;
        $updated = 0;
        $createdValues = 0;
        $missingProducts = 0;
        $errors = 0;
        $cleared = 0;

        // Group rows by product for efficient compare
        $byProduct = [];
        foreach ($payload as $row) {
            if (!isset($row['id_product'])) {
                continue;
            }
            $pid = (int)$row['id_product'];
            if (!empty($productsFilter) && !isset($productsFilter[$pid])) {
                continue;
            }
            $byProduct[$pid][] = $row;
        }

        // If a filter is provided, also consider products in the list that are ABSENT from the payload
        // as products that should have NO features (clear them).
        if (!empty($productsFilter)) {
            foreach ($productsFilter as $pid => $_true) {
                if (!isset($byProduct[$pid])) {
                    // Only clear if product exists locally
                    $exists = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` WHERE id_product = ' . (int)$pid);
                    if ($exists > 0) {
                        // Check if there are any features to clear
                        $has = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'feature_product` WHERE id_product = ' . (int)$pid);
                        if ($has > 0) {
                            $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE id_product = ' . (int)$pid);
                            $cleared++;
                        } else {
                            $unchanged++;
                        }
                        $processed++;
                    } else {
                        $missingProducts++;
                    }
                }
            }
        } elseif ($clearAbsentAll) {
            // No filter: clear features for ALL target products that are not present in payload
            $allLocalIds = $db->executeS('SELECT id_product FROM `' . _DB_PREFIX_ . 'product`');
            if (is_array($allLocalIds)) {
                foreach ($allLocalIds as $rowId) {
                    $pid = (int)$rowId['id_product'];
                    if ($pid <= 0) { continue; }
                    if (isset($byProduct[$pid])) { continue; }
                    // Check if there are any features to clear
                    $has = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'feature_product` WHERE id_product = ' . (int)$pid);
                    if ($has > 0) {
                        $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE id_product = ' . (int)$pid);
                        $cleared++;
                    } else {
                        $unchanged++;
                    }
                    $processed++;
                }
            }
        }

        foreach ($byProduct as $idProduct => $rows) {
            $idProduct = (int)$idProduct;
            if ($idProduct <= 0) {
                continue;
            }

            // Ensure product exists locally
            $exists = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` WHERE id_product = ' . (int)$idProduct);
            if ($exists <= 0) {
                $missingProducts++;
                continue;
            }

            // Fetch current features for product
            $current = $db->executeS(
                'SELECT fp.id_feature, fp.id_feature_value, fv.custom
                 FROM `' . _DB_PREFIX_ . 'feature_product` fp
                 INNER JOIN `' . _DB_PREFIX_ . 'feature_value` fv ON fv.id_feature_value = fp.id_feature_value
                 WHERE fp.id_product = ' . (int)$idProduct
            );
            $currentMap = [];
            if (is_array($current)) {
                foreach ($current as $r) {
                    $currentMap[(int)$r['id_feature']] = [
                        'id_feature_value' => (int)$r['id_feature_value'],
                        'custom' => (int)$r['custom'],
                    ];
                }
            }

            // Build desired map from payload
            $desiredMap = [];
            foreach ($rows as $r) {
                $fid = (int)$r['id_feature'];
                $fvid = (int)$r['id_feature_value'];
                $custom = isset($r['custom']) ? (int)$r['custom'] : 0;

                // Verify feature exists. If not, try match by names (fallback)
                if ($fid > 0) {
                    $hasFeature = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'feature` WHERE id_feature = ' . $fid);
                    if ($hasFeature <= 0 && !empty($r['feature']) && is_array($r['feature'])) {
                        // Try find by any language name
                        $foundFid = 0;
                        foreach ($r['feature'] as $iso => $name) {
                            $name = pSQL((string)$name);
                            if ($name === '') { continue; }
                            $found = $db->getValue('SELECT id_feature FROM `' . _DB_PREFIX_ . 'feature_lang` WHERE name = "' . $name . '"');
                            if ($found) { $foundFid = (int)$found; break; }
                        }
                        if ($foundFid > 0) { $fid = $foundFid; }
                    }
                }

                // Verify feature value exists; if not and non-custom, try find/create by text
                if ($fvid > 0) {
                    $hasValue = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'feature_value` WHERE id_feature_value = ' . $fvid);
                    if ($hasValue <= 0) {
                        $fvid = 0; // force re-find/create below
                    }
                }

                if ($fvid <= 0 && !empty($r['value']) && is_array($r['value'])) {
                    // Try match by localized value text in any language
                    $foundFvid = 0;
                    foreach ($r['value'] as $iso => $valueText) {
                        $valueText = pSQL((string)$valueText);
                        if ($valueText === '') { continue; }
                        $found = $db->getValue('SELECT id_feature_value FROM `' . _DB_PREFIX_ . 'feature_value_lang` WHERE value = "' . $valueText . '"');
                        if ($found) { $foundFvid = (int)$found; break; }
                    }
                    if ($foundFvid > 0) {
                        $fvid = $foundFvid;
                    } else {
                        // Create new value for this feature
                        if ($fid > 0) {
                            $db->execute('INSERT INTO `' . _DB_PREFIX_ . 'feature_value` (id_feature, custom) VALUES (' . (int)$fid . ', 0)');
                            $newId = (int)$db->Insert_ID();
                            if ($newId > 0) {
                                foreach ($r['value'] as $iso => $valueText) {
                                    if (!isset($isoToLang[$iso])) { continue; }
                                    $idLang = (int)$isoToLang[$iso];
                                    $db->execute(
                                        'INSERT INTO `' . _DB_PREFIX_ . 'feature_value_lang` (id_feature_value, id_lang, value) VALUES (' . (int)$newId . ', ' . (int)$idLang . ', "' . pSQL((string)$valueText) . '")'
                                    );
                                }
                                $fvid = $newId;
                                $createdValues++;
                            }
                        }
                    }
                }

                if ($fid > 0 && $fvid > 0) {
                    $desiredMap[$fid] = [
                        'id_feature_value' => $fvid,
                        'custom' => $custom,
                    ];
                }
            }

            // Compare current vs desired
            if ($currentMap == $desiredMap) {
                $unchanged++;
                $processed++;
                continue;
            }

            // Replace assignments for this product to match desired
            // Strategy: delete all current feature_product rows for the product, then insert desired.
            $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE id_product = ' . (int)$idProduct);

            foreach ($desiredMap as $fid => $info) {
                $fid = (int)$fid;
                $fvid = (int)$info['id_feature_value'];
                $db->execute(
                    'INSERT INTO `' . _DB_PREFIX_ . 'feature_product` (id_product, id_feature, id_feature_value) VALUES (' . (int)$idProduct . ', ' . $fid . ', ' . $fvid . ')'
                );
            }

            $updated++;
            $processed++;
        }

        // Clear cache
        if (class_exists('Cache')) {
            Cache::clean('*');
        }

        return [
            'ok' => true,
            'processed' => $processed,
            'unchanged' => $unchanged,
            'updated' => $updated,
            'cleared' => $cleared,
            'created_values' => $createdValues,
            'missing_products' => $missingProducts,
            'errors' => $errors,
        ];
    }

    private function importProducts()
    {
        $data = json_decode(file_get_contents('https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_products'), true);

        if (!$data || !is_array($data)) {
            echo 'Could not fetch or decode data.';
            return;
        }

        $lang_results = Db::getInstance()->executeS('SELECT `id_lang`, `iso_code` FROM `' . _DB_PREFIX_ . 'lang` WHERE `active` = 1');
        $lang_map = [];
        foreach ($lang_results as $lang) {
            $lang_map[$lang['iso_code']] = (int)$lang['id_lang'];
        }

        foreach ($data as $row) {
            if (!isset($row['id_product']) || !isset($row['id_shop'])) {
                continue;
            }

            $id_product = (int)$row['id_product'];
            $id_shop = (int)$row['id_shop'];

            // Check if any delivery time text is populated for this product
            $has_specific_delivery_text = false;
            if (isset($row['lang_data']) && is_array($row['lang_data'])) {
                foreach ($row['lang_data'] as $lang_data) {
                    if (!empty($lang_data['delivery_in_stock']) && !empty($lang_data['delivery_out_stock'])) {
                        $has_specific_delivery_text = true;
                        break;
                    }
                }
            }

            // If it has specific text, set the product to use specific delivery times
            if ($has_specific_delivery_text) {
                // Try both field names for different PrestaShop versions
                $updateData = [];

                // Check which field exists in the product table
                $columns = Db::getInstance()->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "additional_delivery_times"');
                if (!empty($columns)) {
                    $updateData['additional_delivery_times'] = 2; // Specific delivery time for this product
                }

                $columns = Db::getInstance()->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "delivery_time_note_type"');
                if (!empty($columns)) {
                    $updateData['delivery_time_note_type'] = 2; // Specific delivery time for this product
                }

                if (!empty($updateData)) {
                    Db::getInstance()->update('product', $updateData, 'id_product = ' . $id_product);
                }
            }

            // Update product_shop with non-language specific data
            $updateData = [
                'unity' => pSQL($row['unity']),
            ];

            // Handle unit price - both old and new versions should have unit_price field
            if (isset($row['unit_price']) && (float)$row['unit_price'] > 0) {
                $updateData['unit_price'] = (float)$row['unit_price'];
            }

            Db::getInstance()->update(
                'product_shop',
                $updateData,
                'id_product = ' . $id_product . ' AND id_shop = ' . $id_shop
            );

            // Loop through nested language data and update product_lang
            if (isset($row['lang_data']) && is_array($row['lang_data'])) {
                foreach ($row['lang_data'] as $iso_code => $lang_data) {
                    if (isset($lang_map[$iso_code])) {
                        $id_lang = $lang_map[$iso_code];
                        Db::getInstance()->update(
                            'product_lang',
                            [
                                'delivery_in_stock' => pSQL($lang_data['delivery_in_stock']),
                                'delivery_out_stock' => pSQL($lang_data['delivery_out_stock']),
                            ],
                            'id_product = ' . $id_product . ' AND id_lang = ' . $id_lang . ' AND id_shop = ' . $id_shop
                        );
                    }
                }
            }
        }
    }

    private function importAddresses()
    {
        $data = json_decode(file_get_contents('https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export'), true);

        foreach ($data as $row) {
            if (!is_array($row)) {
                continue;
            }
            $columns = implode(', ', array_keys($row));
            $values = implode(', ', array_map(function ($value) {
                return '"' . pSQL($value) . '"';
            }, $row));

            Db::getInstance()->execute(
                'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'address` (' . $columns . ') VALUES (' . $values . ')'
            );
        }
    }

    private function importTables()
    {
        $data = json_decode(file_get_contents('https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export'), true);

        foreach ($data as $table => $rows) {
            Db::getInstance()->execute('TRUNCATE TABLE `' . _DB_PREFIX_ . $table . '`');
            foreach ($rows as $row) {
                $columns = implode(', ', array_keys($row));
                $values = implode(', ', array_map(function ($value) {
                    return '"' . pSQL($value) . '"';
                }, $row));

                Db::getInstance()->execute(
                    'INSERT INTO `' . _DB_PREFIX_ . $table . '` (' . $columns . ') VALUES (' . $values . ')'
                );
            }
        }
    }

    /**
     * Import product stock to match old shop exactly using stock_available.
     * - Works for product and combinations.
     * - Optional $products_import to restrict which products to modify.
     * - Clears stock rows for filtered products absent from payload; when filter empty, you can toggle $clearAbsentAll.
     *
     * @param string|array|null $dataOrUrl
     * @return array{ok:bool,processed:int,unchanged:int,updated:int,inserted:int,cleared:int,missing_products:int,errors:int}
     */
    private function importProductStock($dataOrUrl = null)
    {
        $db = Db::getInstance();

        // Restrict which products to touch. Empty = process only those in payload by default.
        $products_import = array();
        $productsFilter = [];
        foreach ($products_import as $pid) {
            $pid = (int)$pid;
            if ($pid > 0) { $productsFilter[$pid] = true; }
        }
        // When no filter is provided, toggle whether to clear absent products entirely.
        $clearAbsentAll = true; // set to false to avoid clearing for products absent from payload

        // Load payload
        if ($dataOrUrl === null) {
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_product_stock';
        }
        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }
        if (!is_array($payload)) {
            return ['ok' => false, 'processed' => 0, 'unchanged' => 0, 'updated' => 0, 'inserted' => 0, 'cleared' => 0, 'missing_products' => 0, 'errors' => 1];
        }

        $processed = 0;
        $unchanged = 0;
        $updated = 0;
        $inserted = 0;
        $cleared = 0;
        $missingProducts = 0;
        $errors = 0;

        // Detect optional columns in target
        $hasReserved = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'stock_available` LIKE "reserved_quantity"'));
        $hasAvailableDate = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'stock_available` LIKE "available_date"'));

        // Group payload by id_product for faster handling and filter application
        $byProduct = [];
        foreach ($payload as $row) {
            if (!isset($row['id_product'])) { continue; }
            $pid = (int)$row['id_product'];
            if (!empty($productsFilter) && !isset($productsFilter[$pid])) { continue; }
            $byProduct[$pid][] = $row;
        }

        // Clear absent products according to filter/toggle
        if (!empty($productsFilter)) {
            foreach ($productsFilter as $pid => $_) {
                if (!isset($byProduct[$pid])) {
                    // Clear all stock_available rows for this product
                    $has = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid);
                    if ($has > 0) {
                        $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid);
                        $cleared++;
                    } else {
                        $unchanged++;
                    }
                    $processed++;
                }
            }
        } elseif ($clearAbsentAll) {
            $allLocal = $db->executeS('SELECT id_product FROM `' . _DB_PREFIX_ . 'product`');
            foreach ($allLocal as $p) {
                $pid = (int)$p['id_product'];
                if ($pid <= 0) { continue; }
                if (isset($byProduct[$pid])) { continue; }
                $has = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid);
                if ($has > 0) {
                    $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid);
                    $cleared++;
                } else {
                    $unchanged++;
                }
                $processed++;
            }
        }

        // Synchronize each product present in payload
        foreach ($byProduct as $pid => $rows) {
            $pid = (int)$pid;
            // Build desired set: key by (id_product_attribute, id_shop, id_shop_group)
            $desired = [];
            foreach ($rows as $r) {
                $ipa = isset($r['id_product_attribute']) ? (int)$r['id_product_attribute'] : 0;
                $idShop = isset($r['id_shop']) ? (int)$r['id_shop'] : 0;
                $idShopGroup = isset($r['id_shop_group']) ? (int)$r['id_shop_group'] : 0;
                $key = $ipa . ':' . $idShop . ':' . $idShopGroup;
                $desired[$key] = [
                    'quantity' => (int)$r['quantity'],
                    'depends_on_stock' => (int)$r['depends_on_stock'],
                    'out_of_stock' => (int)$r['out_of_stock'],
                    'ipa' => $ipa,
                    'id_shop' => $idShop,
                    'id_shop_group' => $idShopGroup,
                ];
            }

            // Fetch current stock rows for this product
            $current = $db->executeS('SELECT id_product_attribute, id_shop, id_shop_group, quantity, depends_on_stock, out_of_stock' . ($hasReserved ? ', reserved_quantity' : '') . ($hasAvailableDate ? ', available_date' : '') . ' FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid);
            $currentMap = [];
            foreach ($current as $c) {
                $key = (int)$c['id_product_attribute'] . ':' . (int)$c['id_shop'] . ':' . (int)$c['id_shop_group'];
                $currentMap[$key] = [
                    'quantity' => (int)$c['quantity'],
                    'depends_on_stock' => (int)$c['depends_on_stock'],
                    'out_of_stock' => (int)$c['out_of_stock'],
                    'reserved_quantity' => $hasReserved ? (int)$c['reserved_quantity'] : 0,
                    'available_date' => $hasAvailableDate ? (string)$c['available_date'] : null,
                ];
            }

            // Delete rows that should not exist
            foreach ($currentMap as $key => $_row) {
                if (!isset($desired[$key])) {
                    list($ipa, $idShop, $idShopGroup) = array_map('intval', explode(':', $key));
                    $db->execute('DELETE FROM `' . _DB_PREFIX_ . 'stock_available` WHERE id_product = ' . (int)$pid . ' AND id_product_attribute = ' . $ipa . ' AND id_shop = ' . $idShop . ' AND id_shop_group = ' . $idShopGroup);
                    $updated++;
                }
            }

            // Insert or update desired rows
            foreach ($desired as $key => $d) {
                $exists = isset($currentMap[$key]);
                if ($exists) {
                    $cur = $currentMap[$key];
                    $match = (
                        $cur['quantity'] == $d['quantity'] &&
                        $cur['depends_on_stock'] == $d['depends_on_stock'] &&
                        $cur['out_of_stock'] == $d['out_of_stock']
                    );
                    if ($match) {
                        $unchanged++;
                    } else {
                        $set = [
                            'quantity' => (int)$d['quantity'],
                            'depends_on_stock' => (int)$d['depends_on_stock'],
                            'out_of_stock' => (int)$d['out_of_stock'],
                        ];
                        if ($hasReserved && isset($d['reserved_quantity'])) { $set['reserved_quantity'] = (int)$d['reserved_quantity']; }
                        if ($hasAvailableDate && array_key_exists('available_date', $d)) { $set['available_date'] = pSQL((string)$d['available_date']); }
                        $db->update(
                            'stock_available',
                            $set,
                            'id_product = ' . (int)$pid .
                            ' AND id_product_attribute = ' . (int)$d['ipa'] .
                            ' AND id_shop = ' . (int)$d['id_shop'] .
                            ' AND id_shop_group = ' . (int)$d['id_shop_group']
                        );
                        $updated++;
                    }
                } else {
                    $columns = ['id_product','id_product_attribute','id_shop','id_shop_group','quantity','depends_on_stock','out_of_stock'];
                    $values = [
                        (int)$pid,
                        (int)$d['ipa'],
                        (int)$d['id_shop'],
                        (int)$d['id_shop_group'],
                        (int)$d['quantity'],
                        (int)$d['depends_on_stock'],
                        (int)$d['out_of_stock'],
                    ];
                    if ($hasReserved) { $columns[] = 'reserved_quantity'; $values[] = isset($d['reserved_quantity']) ? (int)$d['reserved_quantity'] : 0; }
                    if ($hasAvailableDate) { $columns[] = 'available_date'; $values[] = isset($d['available_date']) ? '"' . pSQL((string)$d['available_date']) . '"' : 'NULL'; }

                    if ($hasAvailableDate) {
                        $db->execute(
                            'INSERT INTO `' . _DB_PREFIX_ . 'stock_available` (' . implode(',', $columns) . ') VALUES (' .
                            (int)$pid . ', ' . (int)$d['ipa'] . ', ' . (int)$d['id_shop'] . ', ' . (int)$d['id_shop_group'] . ', ' . (int)$d['quantity'] . ', ' . (int)$d['depends_on_stock'] . ', ' . (int)$d['out_of_stock'] . ', ' . ($hasReserved ? (isset($d['reserved_quantity']) ? (int)$d['reserved_quantity'] : 0) : 0) . ', ' . (isset($d['available_date']) ? '"' . pSQL((string)$d['available_date']) . '"' : 'NULL') . ')'
                        );
                    } else {
                        $db->execute(
                            'INSERT INTO `' . _DB_PREFIX_ . 'stock_available` (' . implode(',', $columns) . ') VALUES (' .
                            (int)$pid . ', ' . (int)$d['ipa'] . ', ' . (int)$d['id_shop'] . ', ' . (int)$d['id_shop_group'] . ', ' . (int)$d['quantity'] . ', ' . (int)$d['depends_on_stock'] . ', ' . (int)$d['out_of_stock'] . ( $hasReserved ? (', ' . (isset($d['reserved_quantity']) ? (int)$d['reserved_quantity'] : 0)) : '' ) . ')'
                        );
                    }
                    $inserted++;
                }
                $processed++;
            }
        }

        // Clear cache
        if (class_exists('Cache')) {
            Cache::clean('*');
        }

        return [
            'ok' => true,
            'processed' => $processed,
            'unchanged' => $unchanged,
            'updated' => $updated,
            'inserted' => $inserted,
            'cleared' => $cleared,
            'missing_products' => $missingProducts,
            'errors' => $errors,
        ];
    }

    /**
     * Assign ETS Marketplace sellers to products based on supplier -> invoicer mapping.
     * Action entry: action=products_assign_sellers
     *
     * Filters by product id range: id >= $id_prod_start and id < $id_prod_finish
     * You can pass optional GET params: id_prod_start, id_prod_finish
     *
     * @return array
     */
    public function productsAssignSelllers()
    {
        // Range filters
        $id_prod_start = (int)Tools::getValue('id_prod_start', 0);
        $id_prod_finish = (int)Tools::getValue('id_prod_finish', 0);

        // Validate range: if finish not provided or <= start, consider open-ended
        $useFinish = $id_prod_finish > 0 && $id_prod_finish > $id_prod_start;

        // Ensure ETS Marketplace is available
        if (!Module::isInstalled('ets_marketplace') || !Module::isEnabled('ets_marketplace')) {
            return [
                'ok' => false,
                'reason' => 'ets_marketplace module is not installed or enabled',
            ];
        }
        if (!class_exists('Ets_mp_seller')) {
            // Attempt to load the module so its classes are available
            Module::getInstanceByName('ets_marketplace');
        }
        if (!class_exists('Ets_mp_seller')) {
            return [
                'ok' => false,
                'reason' => 'Ets_mp_seller class not found',
            ];
        }

        $db = Db::getInstance();

        // Build product selection query limited by id range
        $sql = new DbQuery();
        $sql->select('p.id_product, p.id_supplier');
        $sql->from('product', 'p');
        if ($id_prod_start > 0) {
            $sql->where('p.id_product >= ' . (int)$id_prod_start);
        }
        if ($useFinish) {
            $sql->where('p.id_product < ' . (int)$id_prod_finish);
        }

        $products = $db->executeS($sql);
        if (!is_array($products)) {
            return [
                'ok' => false,
                'reason' => 'Query returned no products',
            ];
        }

        // Preload mapping of suppliers -> invoicers (sellers) from th_supplier_data
        $supplierIds = [];
        foreach ($products as $pr) {
            $sid = (int)$pr['id_supplier'];
            if ($sid > 0) { $supplierIds[$sid] = $sid; }
        }

        $supplierToInvoicer = [];
        if (!empty($supplierIds)) {
            $mapSql = new DbQuery();
            $mapSql->select('id_supplier, id_invoicer');
            $mapSql->from('th_supplier_data');
            $mapSql->where('id_supplier IN (' . implode(',', array_map('intval', $supplierIds)) . ')');
            $rows = $db->executeS($mapSql);
            if (is_array($rows)) {
                foreach ($rows as $r) {
                    $supplierToInvoicer[(int)$r['id_supplier']] = (int)$r['id_invoicer'];
                }
            }
        }

        $assigned = 0;
        $already = 0;
        $skippedNoSupplier = 0;
        $skippedNoMap = 0;
        $errors = 0;
        $details = [];

        foreach ($products as $row) {
            $idProduct = (int)$row['id_product'];
            $idSupplier = (int)$row['id_supplier'];

            if ($idSupplier <= 0) {
                $skippedNoSupplier++;
                $details[] = ['id_product' => $idProduct, 'status' => 'skip_no_supplier'];
                continue;
            }

            $idInvoicer = isset($supplierToInvoicer[$idSupplier]) ? (int)$supplierToInvoicer[$idSupplier] : 0;
            if ($idInvoicer <= 0) {
                $skippedNoMap++;
                $details[] = ['id_product' => $idProduct, 'status' => 'skip_no_mapping', 'id_supplier' => $idSupplier];
                continue;
            }

            try {
                $seller = new Ets_mp_seller($idInvoicer);
                if (Validate::isLoadedObject($seller)) {
                    if ($seller->checkHasProduct($idProduct)) {
                        $already++;
                        $details[] = ['id_product' => $idProduct, 'status' => 'already_assigned', 'id_seller' => $idInvoicer];
                    } else {
                        // approve=1, active=1
                        $seller->addProduct($idProduct, 1, 1);
                        $assigned++;
                        $details[] = ['id_product' => $idProduct, 'status' => 'assigned', 'id_seller' => $idInvoicer];
                    }
                } else {
                    $errors++;
                    $details[] = ['id_product' => $idProduct, 'status' => 'error_invalid_seller', 'id_seller' => $idInvoicer];
                }
            } catch (Exception $e) {
                $errors++;
                $details[] = ['id_product' => $idProduct, 'status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return [
            'ok' => true,
            'range' => [
                'id_prod_start' => $id_prod_start,
                'id_prod_finish' => $id_prod_finish,
            ],
            'counts' => [
                'total' => count($products),
                'assigned' => $assigned,
                'already_assigned' => $already,
                'skipped_no_supplier' => $skippedNoSupplier,
                'skipped_no_mapping' => $skippedNoMap,
                'errors' => $errors,
            ],
            'details' => $details,
        ];
    }

    /**
     * Assign orders to ETS Marketplace seller id 1 by mapping into ets_mp_seller_order.
     * Action entry: action=orders_assign_sellers
     *
     * Filters by order id range: id_order >= id_order_start and id_order < id_order_finish
     * Optional GET params: id_order_start, id_order_finish
     *
     * Mapping logic in ETS:
     * - Table: ets_mp_seller_order (id_order, id_customer)
     * - Helper: Ets_mp_seller::addOrderToSeller($id_customer, $id_order)
     * - Seller id -> seller customer id: select id_customer from ets_mp_seller where id_seller = 1
     *
     * For each order in range:
     * - If mapping absent: insert
     * - If mapping exists with different id_customer: update to target
     * - If already mapped to target: skip
     *
     * @return array
     */
    public function ordersAssignSellers()
    {
        // Range filters
        $id_order_start = (int)Tools::getValue('id_order_start', 0);
        $id_order_finish = (int)Tools::getValue('id_order_finish', 0);
        $useFinish = $id_order_finish > 0 && $id_order_finish > $id_order_start;

        // Ensure ETS Marketplace is available
        if (!Module::isInstalled('ets_marketplace') || !Module::isEnabled('ets_marketplace')) {
            return [
                'ok' => false,
                'reason' => 'ets_marketplace module is not installed or enabled',
            ];
        }
        if (!class_exists('Ets_mp_seller')) {
            Module::getInstanceByName('ets_marketplace');
        }
        if (!class_exists('Ets_mp_seller')) {
            return [
                'ok' => false,
                'reason' => 'Ets_mp_seller class not found',
            ];
        }

        $db = Db::getInstance();

        // Resolve target seller id -> id_customer
        $targetSellerId = 1;
        $targetCustomerId = (int)$db->getValue('SELECT id_customer FROM `' . _DB_PREFIX_ . 'ets_mp_seller` WHERE id_seller=' . (int)$targetSellerId);
        if ($targetCustomerId <= 0) {
            return [
                'ok' => false,
                'reason' => 'Could not resolve id_customer for ets seller id 1',
            ];
        }

        // Select orders in the given range
        $q = new DbQuery();
        $q->select('o.id_order');
        $q->from('orders', 'o');
        if ($id_order_start > 0) {
            $q->where('o.id_order >= ' . (int)$id_order_start);
        }
        if ($useFinish) {
            $q->where('o.id_order < ' . (int)$id_order_finish);
        }
        $orders = $db->executeS($q);
        if (!is_array($orders)) {
            return [
                'ok' => false,
                'reason' => 'No orders found for the given range',
            ];
        }

        $assigned = 0;
        $updated = 0;
        $skipped = 0;
        $errors = 0;
        $details = [];

        foreach ($orders as $o) {
            $idOrder = (int)$o['id_order'];
            try {
                $existingIdCustomer = (int)$db->getValue('SELECT id_customer FROM `' . _DB_PREFIX_ . 'ets_mp_seller_order` WHERE id_order=' . (int)$idOrder);
                if ($existingIdCustomer > 0) {
                    if ($existingIdCustomer === $targetCustomerId) {
                        $skipped++;
                        $details[] = ['id_order' => $idOrder, 'status' => 'already_assigned'];
                    } else {
                        // Update mapping to target seller's customer id
                        $db->update('ets_mp_seller_order', ['id_customer' => (int)$targetCustomerId], 'id_order = ' . (int)$idOrder);
                        $updated++;
                        $details[] = ['id_order' => $idOrder, 'status' => 'updated'];
                    }
                } else {
                    // Insert mapping (use helper or direct insert)
                    Ets_mp_seller::addOrderToSeller($targetCustomerId, $idOrder);
                    $assigned++;
                    $details[] = ['id_order' => $idOrder, 'status' => 'assigned'];
                }
            } catch (Exception $e) {
                $errors++;
                $details[] = ['id_order' => $idOrder, 'status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return [
            'ok' => true,
            'range' => [
                'id_order_start' => $id_order_start,
                'id_order_finish' => $id_order_finish,
            ],
            'counts' => [
                'total' => count($orders),
                'assigned' => $assigned,
                'updated' => $updated,
                'skipped' => $skipped,
                'errors' => $errors,
            ],
            'details' => $details,
        ];
    }

    /**
     * Regenerate product images for products in the given id range.
     * Action entry: action=product_regenerate_images
     *
     * Filters by product id: id_product >= id_prod_start and id_product <= id_prod_finish (inclusive)
     * Optional GET params: id_prod_start, id_prod_finish
     *
     * @return array
     */
    public function productRegenerateImages()
    {
        $id_prod_start = (int)Tools::getValue('id_prod_start', 0);
        $id_prod_finish = (int)Tools::getValue('id_prod_finish', 0);
        // Use finish when provided and >= start, so equal start/finish targets a single product
        $useFinish = $id_prod_finish > 0 && $id_prod_finish >= $id_prod_start;

        $db = Db::getInstance();

        // Gather images for products in range
        $q = new DbQuery();
        $q->select('i.id_image, i.id_product');
        $q->from('image', 'i');
        if ($id_prod_start > 0 || $useFinish) {
            $q->innerJoin('product', 'p', 'p.id_product = i.id_product');
            if ($id_prod_start > 0) {
                $q->where('p.id_product >= ' . (int)$id_prod_start);
            }
            if ($useFinish) {
                $q->where('p.id_product <= ' . (int)$id_prod_finish);
            }
        }

        $images = $db->executeS($q);
        if (!is_array($images)) {
            return [
                'ok' => false,
                'reason' => 'No images found for the specified product range',
            ];
        }

        // Load product image types
        $types = ImageType::getImagesTypes('products');
        if (!is_array($types) || empty($types)) {
            return [
                'ok' => false,
                'reason' => 'No image types found for products',
            ];
        }

        $thumbsRegenerated = 0;
        $skippedMissingSrc = 0;
        $errors = 0;
        $details = [];

        foreach ($images as $row) {
            $idImage = (int)$row['id_image'];
            try {
                $image = new Image($idImage);
                if (!Validate::isLoadedObject($image)) {
                    $errors++;
                    $details[] = ['id_image' => $idImage, 'status' => 'error_invalid_image'];
                    continue;
                }
                $base = $image->getExistingImgPath(); // without extension
                // Determine source file (try jpg, jpeg, png)
                $src = null; $srcExt = 'jpg';
                foreach (['jpg', 'jpeg', 'png'] as $ext) {
                    $candidate = _PS_PROD_IMG_DIR_ . $base . '.' . $ext;
                    if (file_exists($candidate)) { $src = $candidate; $srcExt = $ext; break; }
                }
                if (!$src) {
                    $skippedMissingSrc++;
                    $details[] = ['id_image' => $idImage, 'status' => 'skip_missing_source'];
                    continue;
                }

                foreach ($types as $type) {
                    $dest = _PS_PROD_IMG_DIR_ . $base . '-' . $type['name'] . '.' . $srcExt;
                    // Ensure destination directory exists
                    $destDir = dirname($dest);
                    if (!is_dir($destDir)) { @mkdir($destDir, 0775, true); }
                    if (!ImageManager::resize($src, $dest, (int)$type['width'], (int)$type['height'])) {
                        $errors++;
                        $details[] = [
                            'id_image' => $idImage,
                            'type' => $type['name'],
                            'status' => 'resize_failed',
                        ];
                    } else {
                        $thumbsRegenerated++;
                    }
                }
            } catch (Exception $e) {
                $errors++;
                $details[] = ['id_image' => $idImage, 'status' => 'error', 'message' => $e->getMessage()];
            }
        }

        // Optionally clear cache
        if (class_exists('Cache')) { Cache::clean('*'); }

        return [
            'ok' => true,
            'range' => [
                'id_prod_start' => $id_prod_start,
                'id_prod_finish' => $id_prod_finish,
            ],
            'counts' => [
                'images_processed' => count($images),
                'thumbs_regenerated' => $thumbsRegenerated,
                'skipped_missing_source' => $skippedMissingSrc,
                'errors' => $errors,
            ],
            'details' => $details,
        ];
    }

    
    /**
     * Fetch combinations export from old shop and compare to current shop data.
     * Does not modify anything. Reports mismatches per product and combination.
     *
     * @param string|null $dataOrUrl Optional URL of old shop export endpoint or inline payload array
     * @return array
     */
    private function compareCombinations($dataOrUrl = null)
    {
        // Resolve payload
        if ($dataOrUrl === null) {
            // Default endpoint for old shop
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_product_combinations';
        }
        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }
        if (!is_array($payload)) {
            return ['ok' => false, 'reason' => 'Invalid or empty payload'];
        }

        $db = Db::getInstance();
        $idLang = (int)Context::getContext()->language->id;

        $report = [
            'ok' => true,
            'checked_products' => 0,
            'products_missing' => [],
            'products_mismatch' => [],
            'details' => [],
        ];

        foreach ($payload as $item) {
            if (!isset($item['id_product'])) {
                continue;
            }
            $idp = (int)$item['id_product'];
            $report['checked_products']++;

            // Does product exist locally?
            $exists = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` WHERE id_product=' . (int)$idp);
            if ($exists <= 0) {
                $report['products_missing'][] = $idp;
                $report['details'][$idp] = ['status' => 'missing_product'];
                continue;
            }

            // Gather local combinations
            $local = ['byRef' => [], 'byId' => []];
            $pas = $db->executeS('SELECT pa.id_product_attribute, pa.reference, pa.ean13, pa.isbn, pa.upc, pa.mpn, pa.price
                                  FROM `' . _DB_PREFIX_ . 'product_attribute` pa WHERE pa.id_product=' . (int)$idp);
            $paIds = [];
            foreach ((array)$pas as $pa) {
                $ipa = (int)$pa['id_product_attribute'];
                $paIds[] = $ipa;
                $local['byId'][$ipa] = [
                    'reference' => (string)$pa['reference'],
                    'ean13' => (string)$pa['ean13'],
                    'isbn' => (string)$pa['isbn'],
                    'upc' => (string)$pa['upc'],
                    'mpn' => (string)$pa['mpn'],
                    'price' => (float)$pa['price'],
                    'attributes' => [],
                ];
                if ($pa['reference'] !== null && $pa['reference'] !== '') {
                    $local['byRef'][(string)$pa['reference']] = $ipa;
                }
            }
            $localSigNames = []; // ipa => sig
            $sigToIpa = []; // sig => ipa
            if (!empty($paIds)) {
                $in = implode(',', array_map('intval', $paIds));
                $rows = $db->executeS('SELECT pac.id_product_attribute, ag.id_attribute_group, a.id_attribute,
                                               agl.name AS group_name, al.name AS attr_name
                                        FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac
                                        INNER JOIN `' . _DB_PREFIX_ . 'attribute` a ON (a.id_attribute = pac.id_attribute)
                                        INNER JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON (al.id_attribute = a.id_attribute AND al.id_lang = ' . (int)$idLang . ')
                                        INNER JOIN `' . _DB_PREFIX_ . 'attribute_group` ag ON (ag.id_attribute_group = a.id_attribute_group)
                                        INNER JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON (agl.id_attribute_group = ag.id_attribute_group AND agl.id_lang = ' . (int)$idLang . ')
                                        WHERE pac.id_product_attribute IN (' . $in . ')');
                foreach ((array)$rows as $r) {
                    $ipa = (int)$r['id_product_attribute'];
                    $local['byId'][$ipa]['attributes'][] = [
                        'id_attribute_group' => (int)$r['id_attribute_group'],
                        'id_attribute' => (int)$r['id_attribute'],
                        'group_name' => (string)$r['group_name'],
                        'attr_name' => (string)$r['attr_name'],
                    ];
                }
                // build name-based signature for locals
                foreach ($local['byId'] as $ipa => $ldata) {
                    $names = [];
                    foreach ((array)$ldata['attributes'] as $a2) {
                        $names[] = Tools::strtolower(trim((string)($a2['attr_name'] ?? '')));
                    }
                    sort($names, SORT_NATURAL | SORT_FLAG_CASE);
                    $sig = implode('|', $names);
                    $localSigNames[(int)$ipa] = $sig;
                    $sigToIpa[$sig] = (int)$ipa;
                }
            }

            // Compare each old-shop combination with a local match (prefer reference, then NAME-BASED signature)
            $combMismatches = [];
            $matchedSigs = [];
            $missingCombs = [];
            foreach ((array)$item['combinations'] as $comb) {
                $ref = isset($comb['reference']) ? (string)$comb['reference'] : '';
                $ipaLocal = null;
                if ($ref !== '' && isset($local['byRef'][$ref])) {
                    $ipaLocal = (int)$local['byRef'][$ref];
                } else {
                    // fallback by attribute set comparison using NAME-BASED signature
                    $sigOld = '';
                    if (!empty($comb['sig_names'])) {
                        $sigOld = (string)$comb['sig_names'];
                    } else {
                        $names = [];
                        foreach ((array)$comb['attributes'] as $a) {
                            $names[] = Tools::strtolower(trim((string)($a['attr_name'] ?? '')));
                        }
                        sort($names, SORT_NATURAL | SORT_FLAG_CASE);
                        $sigOld = implode('|', $names);
                    }
                    if ($sigOld !== '' && isset($sigToIpa[$sigOld])) {
                        $ipaLocal = (int)$sigToIpa[$sigOld];
                    }
                }

                if ($ipaLocal === null) {
                    $missingCombs[] = ['reference' => $ref, 'label' => (string)($comb['label'] ?? ''), 'reason' => 'no_local_match'];
                    continue;
                }

                // Compare core fields
                $expected = [
                    'ean13' => (string)($comb['ean13'] ?? ''),
                    'isbn' => (string)($comb['isbn'] ?? ''),
                    'upc' => (string)($comb['upc'] ?? ''),
                    'mpn' => (string)($comb['mpn'] ?? ''),
                    'price' => (float)($comb['price'] ?? 0.0),
                ];
                $actual = $local['byId'][$ipaLocal];
                $diffs = [];
                foreach ($expected as $k => $v) {
                    if ((string)$actual[$k] !== (string)$v) {
                        $diffs[$k] = ['old' => $v, 'new' => $actual[$k]];
                    }
                }
                if (!empty($diffs)) {
                    $combMismatches[] = ['reference' => $ref, 'ipa' => $ipaLocal, 'diffs' => $diffs];
                }
                // mark matched signature if available
                if (!empty($comb['sig_names'])) {
                    $matchedSigs[(string)$comb['sig_names']] = true;
                }
            }

            // Detect extra local combinations (present locally but not in old shop payload)
            $extraCombs = [];
            foreach ($localSigNames as $ipa => $sig) {
                if (!isset($matchedSigs[$sig])) {
                    // Build label from local names
                    $names = [];
                    foreach ((array)$local['byId'][$ipa]['attributes'] as $a2) {
                        $names[] = (string)($a2['attr_name'] ?? '');
                    }
                    sort($names, SORT_NATURAL | SORT_FLAG_CASE);
                    $label = implode(' - ', $names);
                    $extraCombs[] = [
                        'ipa' => (int)$ipa,
                        'reference' => (string)$local['byId'][$ipa]['reference'],
                        'label' => $label,
                    ];
                }
            }

            // Prepare detail with counts and lists
            $expectedCount = count((array)$item['combinations']);
            $localCount = count($local['byId']);
            $mismatchExists = !empty($combMismatches) || !empty($missingCombs) || !empty($extraCombs);
            if ($mismatchExists) {
                $report['products_mismatch'][] = $idp;
                $report['details'][$idp] = [
                    'status' => 'mismatch',
                    'expected_count' => $expectedCount,
                    'local_count' => $localCount,
                    'missing_count' => count($missingCombs),
                    'extra_count' => count($extraCombs),
                    'combinations' => $combMismatches,
                    'missing_combinations' => $missingCombs,
                    'extra_combinations' => $extraCombs,
                ];
            } else {
                $report['details'][$idp] = ['status' => 'ok', 'expected_count' => $expectedCount, 'local_count' => $localCount];
            }
        }

        return $report;
    }

    private function renderCompareHtml(array $report)
    {
        $checked = (int)($report['checked_products'] ?? 0);
        $missing = is_array($report['products_missing'] ?? null) ? count($report['products_missing']) : 0;
        $mismatched = is_array($report['products_mismatch'] ?? null) ? count($report['products_mismatch']) : 0;
        $matchedOk = max(0, $checked - $missing - $mismatched);

        ob_start();
?>
        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <title>Combinations Compare Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                }

                .stats {
                    margin-bottom: 16px;
                }

                .stats div {
                    margin: 4px 0;
                }

                .ok {
                    color: #177245;
                }

                .warn {
                    color: #a15c00;
                }

                .err {
                    color: #b00020;
                }

                .muted {
                    color: #666;
                }

                details {
                    margin: 6px 0 16px;
                }

                summary {
                    cursor: pointer;
                }

                code {
                    background: #f6f8fa;
                    padding: 2px 4px;
                    border-radius: 3px;
                }

                ul {
                    margin: 4px 0 8px 20px;
                }
            </style>
        </head>

        <body>
            <h1>Combinations Compare Report</h1>
            <div class="stats">
                <div><strong>Total products checked:</strong> <?php echo (int)$checked; ?></div>
                <div class="ok"><strong>Matched OK:</strong> <?php echo (int)$matchedOk; ?></div>
                <div class="warn"><strong>Mismatched:</strong> <?php echo (int)$mismatched; ?></div>
                <div class="muted"><strong>Missing locally:</strong> <?php echo (int)$missing; ?></div>
            </div>

            <?php if ($mismatched > 0): ?>
                <h2>Mismatched products</h2>
                <?php foreach (($report['products_mismatch'] ?? []) as $idp):
                    $detail = $report['details'][$idp] ?? null;
                    if (!is_array($detail) || !isset($detail['combinations'])) continue;
                ?>
                    <details>
                        <summary><strong>Product ID:</strong> <?php echo (int)$idp; ?> — expected: <?php echo (int)($detail['expected_count'] ?? 0); ?>, local: <?php echo (int)($detail['local_count'] ?? 0); ?>, missing: <?php echo (int)($detail['missing_count'] ?? 0); ?>, extra: <?php echo (int)($detail['extra_count'] ?? 0); ?></summary>
                        <?php foreach ((array)$detail['combinations'] as $comb): ?>
                            <div>
                                <div><strong>Combination ref:</strong> <code><?php echo htmlspecialchars((string)($comb['reference'] ?? '')); ?></code> <?php if (isset($comb['ipa'])) {
                                                                                                                                                            echo '(IPA ' . (int)$comb['ipa'] . ')';
                                                                                                                                                        } ?></div>
                                <?php if (isset($comb['reason']) && $comb['reason'] === 'no_local_match'): ?>
                                    <div class="err">No matching combination found locally.</div>
                                <?php elseif (!empty($comb['diffs'])): ?>
                                    <ul>
                                        <?php foreach ($comb['diffs'] as $field => $vals): ?>
                                            <li>
                                                <strong><?php echo htmlspecialchars((string)$field); ?>:</strong>
                                                old=<code><?php echo htmlspecialchars((string)($vals['old'] ?? '')); ?></code>
                                                new=<code><?php echo htmlspecialchars((string)($vals['new'] ?? '')); ?></code>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>

                        <?php if (!empty($detail['missing_combinations'])): ?>
                            <h4>Missing in new shop (from old shop export)</h4>
                            <ul>
                                <?php foreach ($detail['missing_combinations'] as $mc): ?>
                                    <li>
                                        <strong>ref:</strong> <code><?php echo htmlspecialchars((string)($mc['reference'] ?? '')); ?></code>
                                        <?php if (!empty($mc['label'])): ?> — <em><?php echo htmlspecialchars((string)$mc['label']); ?></em><?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>

                        <?php if (!empty($detail['extra_combinations'])): ?>
                            <h4>Extra in new shop (not found in old shop export)</h4>
                            <ul>
                                <?php foreach ($detail['extra_combinations'] as $ec): ?>
                                    <li>
                                        <strong>IPA:</strong> <?php echo (int)($ec['ipa'] ?? 0); ?>,
                                        <strong>ref:</strong> <code><?php echo htmlspecialchars((string)($ec['reference'] ?? '')); ?></code>
                                        <?php if (!empty($ec['label'])): ?> — <em><?php echo htmlspecialchars((string)$ec['label']); ?></em><?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </details>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="ok">All compared products matched.</p>
            <?php endif; ?>

            <?php if ($missing > 0): ?>
                <h2 class="muted">Missing products (not found locally)</h2>
                <p>
                    <?php echo htmlspecialchars(implode(', ', array_map('intval', $report['products_missing']))); ?>
                </p>
            <?php endif; ?>

            <p class="muted">Tip: append <code>&format=json</code> to the URL to get JSON.</p>
        </body>

        </html>
    <?php
        return ob_get_clean();
    }

    /**
     * Synchronize combinations from old shop export into current shop.
     * Assumptions: product ids and combination ids (id_product_attribute) were kept identical.
     * Actions:
     * - For each exported combination:
     *   - If exists locally (same id_product_attribute): update fields and attribute mapping to match old
     *   - If missing locally: create with the SAME id_product_attribute and mapped attributes
     * - Remove extra local combinations not present in old payload
     */
    private function syncCombinations($dataOrUrl = null)
    {
        // Resolve payload
        if ($dataOrUrl === null) {
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=export_product_combinations';
        }
        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }
        if (!is_array($payload)) {
            return ['ok' => false, 'reason' => 'Invalid or empty payload'];
        }

        $db = Db::getInstance();
        $idLang = (int)Context::getContext()->language->id;

        // Build maps for local attribute groups/values by names (current language)
        $groupMap = []; // group_name => id_attribute_group
        $attrMapByNames = []; // group_name => attr_name => id_attribute
        $groups = $db->executeS('SELECT ag.id_attribute_group, agl.name FROM `' . _DB_PREFIX_ . 'attribute_group` ag INNER JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON (agl.id_attribute_group=ag.id_attribute_group AND agl.id_lang=' . (int)$idLang . ')');
        foreach ((array)$groups as $g) {
            $gname = Tools::strtolower(trim((string)$g['name']));
            $groupMap[$gname] = (int)$g['id_attribute_group'];
        }
        if (!empty($groupMap)) {
            $rows = $db->executeS('SELECT a.id_attribute, a.id_attribute_group, agl.name AS group_name, al.name AS attr_name
                FROM `' . _DB_PREFIX_ . 'attribute` a
                INNER JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON (al.id_attribute=a.id_attribute AND al.id_lang=' . (int)$idLang . ')
                INNER JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON (agl.id_attribute_group=a.id_attribute_group AND agl.id_lang=' . (int)$idLang . ')');
            foreach ((array)$rows as $r) {
                $gname = Tools::strtolower(trim((string)$r['group_name']));
                $aname = Tools::strtolower(trim((string)$r['attr_name']));
                if (!isset($attrMapByNames[$gname])) {
                    $attrMapByNames[$gname] = [];
                }
                $attrMapByNames[$gname][$aname] = (int)$r['id_attribute'];
            }
        }

        // Detect presence of product_attribute_shop for multishop price column
        $hasPAShop = !empty($db->executeS('SHOW TABLES LIKE "' . pSQL(_DB_PREFIX_ . 'product_attribute_shop') . '"'));
        $idShop = (int)Context::getContext()->shop->id;

        $result = [
            'ok' => true,
            'updated' => 0,
            'created' => 0,
            'attributes_updated' => 0,
            'deleted_extras' => 0,
            'skipped_missing_product' => 0,
            'skipped_unmapped_attributes' => 0,
            'details' => [],
        ];

        // Optional filter: only process products labeled mismatch by compare_combinations
        $allowedIds = null; // null means all
        $compareUrl = Tools::getValue('compare_url');
        $onlyMismatch = (bool)Tools::getValue('only_mismatch', false);
        if ($compareUrl) {
            // Accept full JSON from external compare endpoint
            $cmp = json_decode(@file_get_contents($compareUrl), true);
            if (is_array($cmp) && isset($cmp['products_mismatch']) && is_array($cmp['products_mismatch'])) {
                $allowedIds = [];
                foreach ($cmp['products_mismatch'] as $pid) {
                    $pid = (int)$pid;
                    if ($pid > 0) {
                        $allowedIds[$pid] = true;
                    }
                }
            }
        } elseif ($onlyMismatch) {
            // Compute mismatches locally without HTTP call, using same export URL param if provided
            $localCompare = $this->compareCombinations(Tools::getValue('url') ?: null);
            if (is_array($localCompare) && isset($localCompare['products_mismatch']) && is_array($localCompare['products_mismatch'])) {
                $allowedIds = [];
                foreach ($localCompare['products_mismatch'] as $pid) {
                    $pid = (int)$pid;
                    if ($pid > 0) {
                        $allowedIds[$pid] = true;
                    }
                }
            }
        }
        // Also allow explicit ids=comma,separated
        $idsParam = Tools::getValue('ids');
        if ($idsParam) {
            if ($allowedIds === null) {
                $allowedIds = [];
            }
            foreach (explode(',', (string)$idsParam) as $pid) {
                $pid = (int)trim($pid);
                if ($pid > 0) {
                    $allowedIds[$pid] = true;
                }
            }
        }

        foreach ($payload as $item) {
            if (!isset($item['id_product'])) {
                continue;
            }
            $idp = (int)$item['id_product'];
            if ($allowedIds !== null && empty($allowedIds[$idp])) {
                continue;
            }
            $exists = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` WHERE id_product=' . (int)$idp);
            if ($exists <= 0) {
                $result['skipped_missing_product']++;
                $result['details'][$idp] = ['status' => 'missing_product'];
                continue;
            }

            // Build local combinations index for fast match
            $localByRef = [];
            $localById = [];
            $pas = $db->executeS('SELECT pa.id_product_attribute, pa.reference, pa.ean13, pa.isbn, pa.upc, pa.mpn, pa.price
                                   FROM `' . _DB_PREFIX_ . 'product_attribute` pa WHERE pa.id_product=' . (int)$idp);
            $paIds = [];
            foreach ((array)$pas as $pa) {
                $ipa = (int)$pa['id_product_attribute'];
                $paIds[] = $ipa;
                $localById[$ipa] = [
                    'reference' => (string)$pa['reference'],
                    'ean13' => (string)$pa['ean13'],
                    'isbn' => (string)$pa['isbn'],
                    'upc' => (string)$pa['upc'],
                    'mpn' => (string)$pa['mpn'],
                    'price' => (float)$pa['price'],
                ];
                if ($pa['reference'] !== null && $pa['reference'] !== '') {
                    $localByRef[(string)$pa['reference']] = $ipa;
                }
            }
            $attrSetsByIPA = []; // ipa => signature string (ids)
            $attrIdsByIPA = []; // ipa => [id_attribute]
            if (!empty($paIds)) {
                $in = implode(',', array_map('intval', $paIds));
                $rows = $db->executeS('SELECT pac.id_product_attribute, a.id_attribute, a.id_attribute_group
                                        FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac
                                        INNER JOIN `' . _DB_PREFIX_ . 'attribute` a ON (a.id_attribute=pac.id_attribute)
                                        WHERE pac.id_product_attribute IN (' . $in . ')');
                $tmp = [];
                foreach ((array)$rows as $r) {
                    $ipa = (int)$r['id_product_attribute'];
                    $tmp[$ipa][] = ((int)$r['id_attribute_group']) . ':' . ((int)$r['id_attribute']);
                    $attrIdsByIPA[$ipa][] = (int)$r['id_attribute'];
                }
                foreach ($tmp as $ipa => $sigArr) {
                    sort($sigArr);
                    $attrSetsByIPA[$ipa] = implode('|', $sigArr);
                    if (isset($attrIdsByIPA[$ipa])) {
                        sort($attrIdsByIPA[$ipa]);
                    }
                }
            }

            // Build maps from old payload by IPA
            $oldIpaSet = [];
            $oldFieldsByIPA = [];
            $oldAttrIdsByIPA = [];
            foreach ((array)$item['combinations'] as $comb) {
                $ipaOld = isset($comb['id_product_attribute']) ? (int)$comb['id_product_attribute'] : 0;
                if ($ipaOld <= 0) {
                    continue;
                }
                $oldIpaSet[$ipaOld] = true;
                $oldFieldsByIPA[$ipaOld] = [
                    'reference' => (string)($comb['reference'] ?? ''),
                    'ean13' => (string)($comb['ean13'] ?? ''),
                    'isbn' => (string)($comb['isbn'] ?? ''),
                    'upc' => (string)($comb['upc'] ?? ''),
                    'mpn' => (string)($comb['mpn'] ?? ''),
                    'price' => (float)($comb['price'] ?? 0.0),
                ];
                // Map attribute names to local id_attribute
                $ids = [];
                foreach ((array)$comb['attributes'] as $a) {
                    $gname = Tools::strtolower(trim((string)$a['group_name']));
                    $aname = Tools::strtolower(trim((string)$a['attr_name']));
                    if (isset($attrMapByNames[$gname][$aname])) {
                        $ids[] = (int)$attrMapByNames[$gname][$aname];
                    }
                }
                sort($ids);
                $oldAttrIdsByIPA[$ipaOld] = $ids;
            }

            $updates = 0;
            $creates = 0;
            $attrUpdated = 0;
            $deleted = 0;
            $unmapped = 0;
            $itemDetails = [];

            // Update or create to match old payload
            foreach (array_keys($oldIpaSet) as $ipaOld) {
                $fields = $oldFieldsByIPA[$ipaOld];
                $targetAttrIds = $oldAttrIdsByIPA[$ipaOld] ?? [];
                // verify that all targetAttrIds are non-empty
                if (empty($targetAttrIds)) {
                    $unmapped++;
                    $result['skipped_unmapped_attributes']++;
                    $itemDetails[] = ['ipa' => $ipaOld, 'status' => 'skip_unmapped_attributes'];
                    continue;
                }
                if (isset($localById[$ipaOld])) {
                    // update fields
                    $set = [];
                    foreach ($fields as $k => $v) {
                        if (!isset($localById[$ipaOld][$k]) || (string)$localById[$ipaOld][$k] !== (string)$v) {
                            $set[$k] = ($k === 'price') ? (float)$v : pSQL((string)$v);
                        }
                    }
                    if (!empty($set)) {
                        $db->update('product_attribute', $set, 'id_product_attribute=' . (int)$ipaOld);
                        if ($hasPAShop && isset($set['price'])) {
                            $rowExists = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product_attribute_shop` WHERE id_product_attribute=' . (int)$ipaOld . ' AND id_shop=' . (int)$idShop);
                            if ($rowExists > 0) {
                                $db->update('product_attribute_shop', ['price' => (float)$fields['price']], 'id_product_attribute=' . (int)$ipaOld . ' AND id_shop=' . (int)$idShop);
                            } else {
                                $db->insert('product_attribute_shop', [
                                    'id_product_attribute' => (int)$ipaOld,
                                    'id_shop' => (int)$idShop,
                                    'price' => (float)$fields['price'],
                                ], false, true, Db::REPLACE);
                            }
                        }
                        $updates++;
                        $itemDetails[] = ['ipa' => $ipaOld, 'status' => 'updated', 'set' => $set];
                    }
                    // sync attributes
                    $localIds = $attrIdsByIPA[$ipaOld] ?? [];
                    sort($localIds);
                    if ($localIds !== $targetAttrIds) {
                        $db->delete('product_attribute_combination', 'id_product_attribute=' . (int)$ipaOld);
                        foreach ($targetAttrIds as $aid) {
                            $db->insert('product_attribute_combination', [
                                'id_attribute' => (int)$aid,
                                'id_product_attribute' => (int)$ipaOld,
                            ]);
                        }
                        $attrUpdated++;
                        $itemDetails[] = ['ipa' => $ipaOld, 'status' => 'attributes_synced', 'count' => count($targetAttrIds)];
                    }
                } else {
                    // create missing combination with same IPA id
                    $db->insert('product_attribute', [
                        'id_product_attribute' => (int)$ipaOld,
                        'id_product' => (int)$idp,
                        'reference' => pSQL($fields['reference']),
                        'ean13' => pSQL($fields['ean13']),
                        'isbn' => pSQL($fields['isbn']),
                        'upc' => pSQL($fields['upc']),
                        'mpn' => pSQL($fields['mpn']),
                        'price' => (float)$fields['price'],
                        'default_on' => null,
                    ]);
                    if ($hasPAShop) {
                        $db->insert('product_attribute_shop', [
                            'id_product_attribute' => (int)$ipaOld,
                            'id_shop' => (int)$idShop,
                            'price' => (float)$fields['price'],
                        ], false, true, Db::REPLACE);
                    }
                    foreach ($targetAttrIds as $aid) {
                        $db->insert('product_attribute_combination', [
                            'id_attribute' => (int)$aid,
                            'id_product_attribute' => (int)$ipaOld,
                        ]);
                    }
                    $creates++;
                    $itemDetails[] = ['ipa' => $ipaOld, 'status' => 'created', 'attr_count' => count($targetAttrIds)];
                }
            }

            // Remove extra locals
            foreach (array_keys($localById) as $ipaLocal) {
                if (!isset($oldIpaSet[$ipaLocal])) {
                    $db->delete('product_attribute_combination', 'id_product_attribute=' . (int)$ipaLocal);
                    if ($hasPAShop) {
                        $db->delete('product_attribute_shop', 'id_product_attribute=' . (int)$ipaLocal . ' AND id_shop=' . (int)$idShop);
                    }
                    $db->delete('product_attribute', 'id_product_attribute=' . (int)$ipaLocal);
                    $deleted++;
                    $itemDetails[] = ['ipa' => $ipaLocal, 'status' => 'deleted_extra'];
                }
            }

            $result['updated'] += $updates;
            $result['created'] += $creates;
            $result['attributes_updated'] += $attrUpdated;
            $result['deleted_extras'] += $deleted;
            $result['details'][$idp] = [
                'updated' => $updates,
                'created' => $creates,
                'attributes_updated' => $attrUpdated,
                'deleted_extras' => $deleted,
                'skipped_unmapped' => $unmapped,
                'items' => $itemDetails,
            ];
        }

        // Optional cache clear
        if (class_exists('Cache')) {
            Cache::clean('*');
        }

        return $result;
    }

    /**
     * Assign shipping type feature to all non-virtual products (both active and inactive).
     * Uses raw SQL for performance; no Product objects are instantiated.
     *
     * Defaults to id_feature=17 and id_feature_value=46107, overridable via GET:
     *   &id_feature=...&id_feature_value=...
     *
     * @return array JSON-serializable result
     */
    private function assignShippingType()
    {
        $db = Db::getInstance();

        $featureId = (int)Tools::getValue('id_feature', 17);
        $featureValueId = (int)Tools::getValue('id_feature_value', 46106);

        // Count eligible products (non-virtual, regardless of active state)
        $eligible = (int)$db->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` WHERE is_virtual = 0');

        // Perform a single bulk upsert to set/replace the feature value for all eligible products
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'feature_product` (id_feature, id_product, id_feature_value) '
            . 'SELECT ' . (int)$featureId . ', p.id_product, ' . (int)$featureValueId . ' FROM `' . _DB_PREFIX_ . 'product` p '
            . 'WHERE p.is_virtual = 0 '
            . 'ON DUPLICATE KEY UPDATE id_feature_value = VALUES(id_feature_value)';

        $ok = (bool)$db->execute($sql);
        if (!$ok) {
            return [
                'ok' => false,
                'feature_id' => $featureId,
                'feature_value_id' => $featureValueId,
                'eligible_products' => $eligible,
                'error' => method_exists($db, 'getMsgError') ? (string)$db->getMsgError() : 'SQL execution failed',
            ];
        }

        // Post-stats: how many products now have this feature assigned
        $assigned = (int)$db->getValue(
            'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'feature_product` fp '
                . 'INNER JOIN `' . _DB_PREFIX_ . 'product` p ON (p.id_product = fp.id_product) '
                . 'WHERE p.is_virtual = 0 AND fp.id_feature = ' . (int)$featureId . ' AND fp.id_feature_value = ' . (int)$featureValueId
        );

        // Optional: clear caches to reflect changes immediately
        if (class_exists('Cache')) {
            Cache::clean('*');
        }

        return [
            'ok' => $ok,
            'feature_id' => $featureId,
            'feature_value_id' => $featureValueId,
            'eligible_products' => $eligible,
            'assigned_now' => $assigned,
        ];
    }

    /**
     * Deep audit of one product's combinations vs what front office can display.
     * - Compares DB combinations (product_attribute) with attribute sets actually attached
     * - Flags duplicate attribute sets (same attributes across multiple IPAs)
     * - Aggregates stock per combination and per attribute value
     * - Helps diagnose cases where FO appears to show more options than BO combinations
     *
     * URL: module/ph_simpleblog/thecon?action=audit_product_combinations&id_product=123&format=html|json
     *
     * @param int $idProduct
     * @return array
     */
    private function auditProductCombinations($idProduct)
    {
        $idProduct = (int)$idProduct;
        $db = Db::getInstance();
        $idLang = (int)Context::getContext()->language->id;
        $idShop = (int)Context::getContext()->shop->id;

        if ($idProduct <= 0) {
            return ['ok' => false, 'reason' => 'Missing or invalid id_product'];
        }

        $exists = (int)$db->getValue('SELECT COUNT(*) FROM `' . pSQL(_DB_PREFIX_) . 'product` WHERE id_product=' . (int)$idProduct);
        if ($exists <= 0) {
            return ['ok' => false, 'id_product' => $idProduct, 'reason' => 'Product not found'];
        }

        // Load combinations (IPAs)
        $ipas = $db->executeS('SELECT pa.id_product_attribute, pa.reference, pa.price, pa.ean13, pa.isbn, pa.upc, pa.mpn, pa.default_on
			FROM `' . pSQL(_DB_PREFIX_) . 'product_attribute` pa
			WHERE pa.id_product=' . (int)$idProduct);
        $ipaIds = array_map(function ($r) {
            return (int)$r['id_product_attribute'];
        }, (array)$ipas);

        $stockByIpa = [];
        if (!empty($ipaIds)) {
            $stockRows = $db->executeS('SELECT id_product_attribute, SUM(quantity) qty
				FROM `' . pSQL(_DB_PREFIX_) . 'stock_available`
				WHERE id_product=' . (int)$idProduct . '
				GROUP BY id_product_attribute');
            foreach ((array)$stockRows as $sr) {
                $stockByIpa[(int)$sr['id_product_attribute']] = (int)$sr['qty'];
            }
        }

        // Attribute mapping per IPA
        $attrsByIpa = [];
        $bySignature = [];
        $groupValues = []; // id_attribute_group => [meta, values]
        if (!empty($ipaIds)) {
            $in = implode(',', array_map('intval', $ipaIds));
            $rows = $db->executeS('SELECT pac.id_product_attribute, a.id_attribute, a.id_attribute_group,
				agl.name AS group_name, al.name AS attr_name
				FROM `' . pSQL(_DB_PREFIX_) . 'product_attribute_combination` pac
				INNER JOIN `' . pSQL(_DB_PREFIX_) . 'attribute` a ON (a.id_attribute=pac.id_attribute)
				INNER JOIN `' . pSQL(_DB_PREFIX_) . 'attribute_lang` al ON (al.id_attribute=a.id_attribute AND al.id_lang=' . (int)$idLang . ')
				INNER JOIN `' . pSQL(_DB_PREFIX_) . 'attribute_group_lang` agl ON (agl.id_attribute_group=a.id_attribute_group AND agl.id_lang=' . (int)$idLang . ')
				WHERE pac.id_product_attribute IN (' . $in . ')');
            foreach ((array)$rows as $r) {
                $ipa = (int)$r['id_product_attribute'];
                $gid = (int)$r['id_attribute_group'];
                $aid = (int)$r['id_attribute'];
                $gname = (string)$r['group_name'];
                $aname = (string)$r['attr_name'];
                $attrsByIpa[$ipa][] = ['id_attribute_group' => $gid, 'id_attribute' => $aid, 'group_name' => $gname, 'attr_name' => $aname];
                if (!isset($groupValues[$gid])) {
                    $groupValues[$gid] = ['id_attribute_group' => $gid, 'group_name' => $gname, 'values' => []];
                }
                if (!isset($groupValues[$gid]['values'][$aid])) {
                    $groupValues[$gid]['values'][$aid] = ['id_attribute' => $aid, 'attr_name' => $aname, 'ipa_count' => 0, 'stock_sum' => 0];
                }
                $groupValues[$gid]['values'][$aid]['ipa_count']++;
                $groupValues[$gid]['values'][$aid]['stock_sum'] += (int)($stockByIpa[$ipa] ?? 0);
            }

            // Build signatures to detect duplicates
            foreach ($attrsByIpa as $ipa => $alist) {
                $parts = [];
                foreach ($alist as $a) {
                    $parts[] = ((int)$a['id_attribute_group']) . ':' . ((int)$a['id_attribute']);
                }
                sort($parts, SORT_NATURAL);
                $sig = implode('|', $parts);
                $bySignature[$sig][] = (int)$ipa;
            }
        }

        // Front rendering perspective: how many attribute values would be shown per group considering PS_DISP_UNAVAILABLE_ATTR and stock
        $hideUnavailable = (int)Configuration::get('PS_DISP_UNAVAILABLE_ATTR') == 0;
        $foGroups = [];
        foreach ($groupValues as $gid => $g) {
            $vals = array_values($g['values']);
            $visible = 0;
            $visibleIds = [];
            foreach ($vals as $v) {
                if (!$hideUnavailable || $v['stock_sum'] > 0 || Product::isAvailableWhenOutOfStock((int)Product::getDefaultAttribute($idProduct))) {
                    $visible++;
                    $visibleIds[] = (int)$v['id_attribute'];
                }
            }
            $foGroups[] = [
                'id_attribute_group' => (int)$gid,
                'group_name' => (string)$g['group_name'],
                'values_total' => (int)count($vals),
                'values_visible' => (int)$visible,
                'visible_attribute_ids' => $visibleIds,
            ];
        }

        $duplicates = [];
        foreach ($bySignature as $sig => $list) {
            if (count($list) > 1) {
                $duplicates[] = ['signature' => $sig, 'ipas' => array_values($list)];
            }
        }

        // Summaries
        $dbCount = count($ipaIds);
        $withStock = 0;
        foreach ($ipaIds as $ipa) {
            if ((int)($stockByIpa[$ipa] ?? 0) > 0) {
                $withStock++;
            }
        }

        return [
            'ok' => true,
            'id_product' => $idProduct,
            'bo_combination_count' => (int)$dbCount,
            'bo_combination_ids' => $ipaIds,
            'combinations_with_stock_gt0' => (int)$withStock,
            'group_values' => array_values($groupValues),
            'frontend_group_visibility' => $foGroups,
            'duplicate_attribute_sets' => $duplicates,
        ];
    }

    private function renderAuditHtml(array $audit)
    {
        ob_start();
    ?>
        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <title>Product Combinations Audit</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                }

                .muted {
                    color: #666;
                }

                .warn {
                    color: #a15c00;
                }

                .err {
                    color: #b00020;
                }

                code {
                    background: #f6f8fa;
                    padding: 2px 4px;
                    border-radius: 3px;
                }

                ul {
                    margin: 4px 0 8px 20px;
                }
            </style>
        </head>

        <body>
            <h1>Product Combinations Audit</h1>
            <?php if (empty($audit['ok'])): ?>
                <p class="err">Error: <?php echo htmlspecialchars((string)($audit['reason'] ?? 'unknown')); ?></p>
            <?php else: ?>
                <div>
                    <div><strong>Product ID:</strong> <?php echo (int)($audit['id_product'] ?? 0); ?></div>
                    <div><strong>BO combinations:</strong> <?php echo (int)($audit['bo_combination_count'] ?? 0); ?></div>
                    <div><strong>Combinations with stock &gt; 0:</strong> <?php echo (int)($audit['combinations_with_stock_gt0'] ?? 0); ?></div>
                </div>

                <h3>Attribute groups (values attached to combinations)</h3>
                <?php foreach ((array)($audit['group_values'] ?? []) as $g): ?>
                    <details>
                        <summary>
                            <strong><?php echo htmlspecialchars((string)($g['group_name'] ?? '')); ?></strong>
                            (<?php echo isset($g['values']) ? count($g['values']) : 0; ?> values)
                        </summary>
                        <ul>
                            <?php if (!empty($g['values'])): foreach ($g['values'] as $v): ?>
                                    <li>
                                        <?php echo (int)$v['id_attribute']; ?> — <?php echo htmlspecialchars((string)$v['attr_name']); ?>
                                        <span class="muted">(in <?php echo (int)$v['ipa_count']; ?> combos, stock sum <?php echo (int)$v['stock_sum']; ?>)</span>
                                    </li>
                            <?php endforeach;
                            endif; ?>
                        </ul>
                    </details>
                <?php endforeach; ?>

                <?php if (!empty($audit['duplicate_attribute_sets'])): ?>
                    <h3 class="warn">Duplicate attribute sets</h3>
                    <ul>
                        <?php foreach ($audit['duplicate_attribute_sets'] as $dup): ?>
                            <li>
                                sig <code><?php echo htmlspecialchars((string)$dup['signature']); ?></code>
                                — IPAs: <code><?php echo htmlspecialchars(implode(', ', array_map('intval', (array)$dup['ipas']))); ?></code>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>

                <h3>Front-end visibility (respecting unavailable attributes setting)</h3>
                <ul>
                    <?php foreach ((array)($audit['frontend_group_visibility'] ?? []) as $fg): ?>
                        <li>
                            <?php echo htmlspecialchars((string)$fg['group_name']); ?>:
                            visible values <?php echo (int)$fg['values_visible']; ?> of <?php echo (int)$fg['values_total']; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </body>

        </html>
<?php
        return ob_get_clean();
    }
}
