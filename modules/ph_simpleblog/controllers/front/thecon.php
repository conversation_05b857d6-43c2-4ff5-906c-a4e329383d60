<?php

class Ph_SimpleBlogTheconModuleFrontController extends ModuleFrontController
{
    public function init()
    {
        // echo 'comment this line before running the script';exit;
        // allow longer processing
        if (function_exists('ignore_user_abort')) {
            @ignore_user_abort(true);
        }
        if (function_exists('set_time_limit')) {
            @set_time_limit(120);
        }
        @ini_set('max_execution_time', '120');

        $action = Tools::getValue('action');

        if ($action === 'update_virtual_products_combinations') {
            header('Content-Type: application/json; charset=utf-8');
            $url = Tools::getValue('url');
            $result = $this->updateVirtualProductsCombinations($url ?: null);
            echo json_encode($result);
            exit;
        }



    }

    


    private function updateVirtualProductsCombinations($dataOrUrl = null)
    {
        $db = Db::getInstance();
        $products_to_update = [66360];

        if ($dataOrUrl === null) {
            $dataOrUrl = 'https://oldshop.sonnenhauswelt.com/module/ph_simpleblog/export?action=exportvirtual_products_combinations';
        }
        if (is_string($dataOrUrl)) {
            $payload = json_decode(@file_get_contents($dataOrUrl), true);
        } else {
            $payload = $dataOrUrl;
        }
        if (!is_array($payload)) {
            return ['ok' => false, 'reason' => 'Invalid payload'];
        }

        // Build a map: id_product => { default_supplier, product_supplier[], ipa_list[] }
        $byId = [];
        foreach ($payload as $row) {
            if (!isset($row['id_product'])) { continue; }
            $pid = (int)$row['id_product']; if ($pid <= 0) { continue; }
            $byId[$pid] = [
                'default_supplier' => isset($row['default_supplier']) ? (int)$row['default_supplier'] : 0,
                'product_supplier' => isset($row['product_supplier']) && is_array($row['product_supplier']) ? $row['product_supplier'] : [],
                'ipa_list' => isset($row['ipa_list']) && is_array($row['ipa_list']) ? array_map('intval', $row['ipa_list']) : [],
            ];
        }

        // Determine target ids respecting manual filter
        $targetIds = array_keys($byId);
        if (!empty($products_to_update)) { $targetIds = array_map('intval', $products_to_update); }

        $updated = 0; $skipped = 0; $errors = 0; $details = [];
        foreach ($targetIds as $idProduct) {
            $idProduct = (int)$idProduct; if ($idProduct <= 0) { continue; }
            try {
                $exists = (int)$db->getValue('SELECT COUNT(*) FROM `'. _DB_PREFIX_ .'product` WHERE id_product='.(int)$idProduct);
                if ($exists <= 0) { $skipped++; $details[] = ['id_product'=>$idProduct,'status'=>'skip_missing']; continue; }

                $hasCombinations = (int)$db->getValue('SELECT COUNT(*) FROM `'. _DB_PREFIX_ .'product_attribute` WHERE id_product='.(int)$idProduct) > 0;
                if (!$hasCombinations) { $skipped++; $details[] = ['id_product'=>$idProduct,'status'=>'skip_no_combinations']; continue; }

                // Repair supplier associations from export payload when provided
                $src = isset($byId[$idProduct]) ? $byId[$idProduct] : null;
                if ($src) {
                    // Build existing map to preserve canonical id_product_supplier
                    $existingRows = $db->executeS('SELECT id_product_supplier, id_supplier, id_product_attribute FROM `'. _DB_PREFIX_ .'product_supplier` WHERE id_product='.(int)$idProduct);
                    $byKey = [];
                    foreach ((array)$existingRows as $ER) {
                        $key = (int)$ER['id_supplier'] . ':' . (int)$ER['id_product_attribute'];
                        if (!isset($byKey[$key])) { $byKey[$key] = []; }
                        $byKey[$key][] = (int)$ER['id_product_supplier'];
                    }

                    // Helper to upsert while keeping the smallest existing id and removing duplicates
                    $ensureRow = function($sid, $ipa, $ref='', $price=0, $idCurrency=0) use ($db, &$byKey, $idProduct) {
                        $sid = (int)$sid; $ipa = (int)$ipa;
                        if ($sid <= 0) { return; }
                        if ((int)$idCurrency <= 0) {
                            $idCurrency = (int)Configuration::get('PS_CURRENCY_DEFAULT');
                        }
                        $key = $sid . ':' . $ipa;
                        if (isset($byKey[$key]) && !empty($byKey[$key])) {
                            rsort($byKey[$key]);
                            $keepId = (int)$byKey[$key][0];
                            // Update the kept row
                            $db->update('product_supplier', [
                                'product_supplier_reference' => pSQL((string)$ref),
                                'product_supplier_price_te' => (float)$price,
                                'id_currency' => (int)$idCurrency,
                            ], 'id_product_supplier='.(int)$keepId);
                            // Remove duplicates for same (sid,ipa)
                            for ($i=1; $i<count($byKey[$key]); $i++) {
                                $db->delete('product_supplier', 'id_product_supplier='.(int)$byKey[$key][$i]);
                            }
                        } else {
                            // Insert fresh row
                            $db->insert('product_supplier', [
                                'id_supplier' => $sid,
                                'id_product' => (int)$idProduct,
                                'id_product_attribute' => $ipa,
                                'product_supplier_reference' => pSQL((string)$ref),
                                'product_supplier_price_te' => (float)$price,
                                'id_currency' => (int)$idCurrency,
                            ]);
                        }
                    };

                    // Ensure default supplier row at IPA 0
                    $defaultSupplier = (int)$src['default_supplier'];
                    if ($defaultSupplier > 0) {
                        // Try to reuse exported ipa=0 mapping data when present
                        $defRef = '';
                        $defPrice = 0;
                        $defCurr = 0;
                        foreach ((array)$src['product_supplier'] as $ps0) {
                            if ((int)($ps0['id_supplier'] ?? 0) === $defaultSupplier && (int)($ps0['id_product_attribute'] ?? -1) === 0) {
                                $defRef = (string)($ps0['product_supplier_reference'] ?? '');
                                $defPrice = (float)($ps0['product_supplier_price_te'] ?? 0);
                                $defCurr = (int)($ps0['id_currency'] ?? 0);
                                break;
                            }
                        }
                        $ensureRow($defaultSupplier, 0, $defRef, $defPrice, $defCurr);
                        // Sync default supplier on product and product_shop (all shops)
                        $db->update('product', ['id_supplier' => $defaultSupplier], 'id_product='.(int)$idProduct);
                    } else {
                        $db->update('product', ['id_supplier' => 0], 'id_product='.(int)$idProduct);
                    }

                    // Ensure per-combination rows (only for combinations that exist locally)
                    $localIpas = $db->executeS('SELECT id_product_attribute FROM `'. _DB_PREFIX_ .'product_attribute` WHERE id_product='.(int)$idProduct);
                    $localSet = [];
                    foreach ((array)$localIpas as $ripa) { $localSet[(int)$ripa['id_product_attribute']] = true; }
                    foreach ((array)$src['product_supplier'] as $ps) {
                        $sid = (int)($ps['id_supplier'] ?? 0);
                        $ipa = (int)($ps['id_product_attribute'] ?? 0);
                        if ($sid > 0 && isset($localSet[$ipa]) && $ipa !== 0) {
                            $ensureRow($sid, $ipa, (string)($ps['product_supplier_reference'] ?? ''), (float)($ps['product_supplier_price_te'] ?? 0), (int)($ps['id_currency'] ?? 0));
                        }
                    }

                    // Do NOT reorder or recreate rows to manipulate IDs.
                    // Keeping stable IDs prevents BO hidden-field mismatches on save.
                }

                // Persist module toggle and enforce flags
                $db->execute('REPLACE INTO `'. _DB_PREFIX_ .'vpc_forced_virtual` (id_product, forced_virtual) VALUES ('.(int)$idProduct.', 1)');
                $db->update('product', ['is_virtual'=>1], 'id_product='.(int)$idProduct);

                // Mark product as combinations in a version-safe way
                $hasProductTypeCol = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "product_type"'));
                if ($hasProductTypeCol) {
                    $db->update('product', ['product_type'=>pSQL('combinations')], 'id_product='.(int)$idProduct);
                }
                $hasProductShop = !empty($db->executeS('SHOW TABLES LIKE "' . pSQL(_DB_PREFIX_ . 'product_shop') . '"'));
                if ($hasProductShop) {
                    $hasPsTypeCol = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product_shop` LIKE "product_type"'));
                    if ($hasPsTypeCol) {
                        $db->update('product_shop', ['product_type'=>pSQL('combinations')], 'id_product='.(int)$idProduct);
                    }
                }

                // Fallback for older versions: ensure cache_default_attribute is set
                $ipaDefault = (int)$db->getValue('SELECT id_product_attribute FROM `'. _DB_PREFIX_ .'product_attribute` WHERE id_product='.(int)$idProduct.' ORDER BY default_on DESC, id_product_attribute ASC');
                if ($ipaDefault > 0) {
                    $db->update('product', ['cache_default_attribute' => $ipaDefault], 'id_product='.(int)$idProduct);
                    if ($hasProductShop) {
                        $db->update('product_shop', ['cache_default_attribute' => $ipaDefault], 'id_product='.(int)$idProduct);
                    }
                }

                $updated++; $details[] = ['id_product'=>$idProduct,'status'=>'updated'];
            } catch (Exception $e) {
                $errors++; $details[] = ['id_product'=>$idProduct,'status'=>'error','message'=>$e->getMessage()];
            }
        }

        if (class_exists('Cache')) { Cache::clean('*'); }
        return ['ok'=>true,'updated'=>$updated,'skipped'=>$skipped,'errors'=>$errors,'details'=>$details];
    }



}
