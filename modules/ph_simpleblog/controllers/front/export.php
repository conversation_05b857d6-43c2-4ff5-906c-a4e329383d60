<?php

class Ph_SimpleBlogExportModuleFrontController extends ModuleFrontController
{
    public function init()
    {

    //    echo 'Comment line 7 before running the script.';exit;

        $action = Tools::getValue('action');

        
        
        if ($action === 'exportvirtual_products_combinations') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportVirtualProductsCombinations();
            echo json_encode($data);
            exit;
        }


        if ($action === 'export_product_vat') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportProductVat();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_product_features') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportProductFeatures();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_product_stock') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportProductStock();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_products') {
            header('Content-Type: application/json; charset=utf-8');
            $limit = (int) Tools::getValue('limit');
            $data = $this->getProductsForExport($limit > 0 ? $limit : 0);
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_product_combinations') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportProductCombinations();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_modules') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->getModulesForExport();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_supplier_addresses') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->getSupplierAddresses();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_raw_supplier_data') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->getRawSupplierData();
            echo json_encode($data);
            exit;
        }

        if ($action === 'export_products_deleted') {
            header('Content-Type: application/json; charset=utf-8');
            $data = $this->exportProductsDeleted();
            echo json_encode($data);
            exit;
        }


    }

    private function exportVirtualProductsCombinations(){
        $db = Db::getInstance();

        // Candidates: virtual products that also have combinations
        $rows = $db->executeS(
            'SELECT DISTINCT p.id_product, p.id_supplier AS default_supplier
             FROM `' . _DB_PREFIX_ . 'product` p
             INNER JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON (pa.id_product = p.id_product)
             WHERE p.is_virtual = 1'
        );
        if (!is_array($rows)) { return []; }

        $out = [];
        foreach ($rows as $r) {
            $pid = (int)$r['id_product'];
            // Export supplier associations for the product (per product and per combination)
            $ps = $db->executeS(
                'SELECT id_supplier, id_product_attribute, product_supplier_reference, product_supplier_price_te, id_currency
                 FROM `' . _DB_PREFIX_ . 'product_supplier`
                 WHERE id_product = ' . (int)$pid
            );
            if (!is_array($ps)) { $ps = []; }

            // Export list of combination ids (sanity)
            $ipas = $db->executeS(
                'SELECT id_product_attribute FROM `' . _DB_PREFIX_ . 'product_attribute` WHERE id_product = ' . (int)$pid
            );
            $ipaList = [];
            foreach ((array)$ipas as $ipaRow) { $ipaList[] = (int)$ipaRow['id_product_attribute']; }

            $out[] = [
                'id_product' => $pid,
                'default_supplier' => (int)$r['default_supplier'],
                'product_supplier' => $ps,
                'ipa_list' => $ipaList,
            ];
        }

        return $out;
    }

    private function getModulesForExport(){
        $modules_on_disk = Module::getModulesOnDisk(true);
        $modules_formatted = [];

        foreach ($modules_on_disk as $module) {
            $modules_formatted[] = [
                'name' => $module->name,
                'display_name' => $module->displayName,
                'installed' => (int)Module::isInstalled($module->name),
                'enabled' => (int)Module::isEnabled($module->name),
            ];
        }

        return $modules_formatted;
    }

    /**
     * Export per-product Tax Rules Group selection for single-shop setup (shop id 1).
     *
     * - Prefers product_shop.id_tax_rules_group for id_shop = 1 when available.
     * - Falls back to product.id_tax_rules_group (legacy/single-shop storage).
     * - Includes tax_rules_group_name to allow robust mapping on import.
     *
     * @return array<int, array<string, mixed>>
     */
    private function exportProductVat()
    {
        $db = Db::getInstance();

        $hasProductShopTable = !empty($db->executeS('SHOW TABLES LIKE "' . pSQL(_DB_PREFIX_ . 'product_shop') . '"'));
        $hasProductShopTax = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product_shop` LIKE "id_tax_rules_group"'));
        $hasProductTax = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "id_tax_rules_group"'));

        $results = [];

        if ($hasProductShopTable && $hasProductShopTax) {
            $query = new DbQuery();
            $query->select('p.id_product');
            $query->select('ps.id_shop');
            $query->select('ps.id_tax_rules_group AS tax_rules_group_id');
            $query->select('trg.name AS tax_rules_group_name');
            $query->from('product_shop', 'ps');
            $query->innerJoin('product', 'p', 'p.id_product = ps.id_product');
            $query->leftJoin('tax_rules_group', 'trg', 'trg.id_tax_rules_group = ps.id_tax_rules_group');

            $results = $db->executeS($query);
        } elseif ($hasProductTax) {
            $query = new DbQuery();
            $query->select('p.id_product');
            $query->select('NULL AS id_shop');
            $query->select('p.id_tax_rules_group AS tax_rules_group_id');
            $query->select('trg.name AS tax_rules_group_name');
            $query->from('product', 'p');
            $query->leftJoin('tax_rules_group', 'trg', 'trg.id_tax_rules_group = p.id_tax_rules_group');

            $results = $db->executeS($query);
        }

        if (empty($results) || !is_array($results)) {
            return [];
        }

        $export = [];
        foreach ($results as $row) {
            $export[] = [
                'id_product' => (int) $row['id_product'],
                'id_shop' => isset($row['id_shop']) ? (int) $row['id_shop'] : null,
                'tax_rules_group_id' => isset($row['tax_rules_group_id']) ? (int) $row['tax_rules_group_id'] : 0,
                'tax_rules_group_name' => isset($row['tax_rules_group_name']) ? (string) $row['tax_rules_group_name'] : null,
            ];
        }

        return $export;
    }

    private function getProductsForExport($limit = 0)
    {
        $sql = new DbQuery();
        $sql->select('p.id_product, p.reference, l.iso_code, pl.id_shop, pl.delivery_in_stock, pl.delivery_out_stock, ps.unit_price, ps.unity');
        $sql->from('product', 'p');
        $sql->leftJoin('product_lang', 'pl', 'p.id_product = pl.id_product');
        $sql->leftJoin('product_shop', 'ps', 'p.id_product = ps.id_product AND pl.id_shop = ps.id_shop');
        $sql->leftJoin('lang', 'l', 'pl.id_lang = l.id_lang');

        if ($limit > 0) {
            $sql->limit($limit);
        }

        $results = Db::getInstance()->executeS($sql);
        $products_formatted = [];

        foreach ($results as $row) {
            $id_product = $row['id_product'];

            if (!isset($products_formatted[$id_product])) {
                $products_formatted[$id_product] = [
                    'id_product'        => $row['id_product'],
                    'reference'         => $row['reference'],
                    'id_shop'           => $row['id_shop'],
                    'unit_price'        => $row['unit_price'],
                    'unity'             => $row['unity'],
                    'lang_data'         => [],
                ];
            }

            $lang_code = $row['iso_code'];
            $products_formatted[$id_product]['lang_data'][$lang_code] = [
                'delivery_in_stock'  => $row['delivery_in_stock'],
                'delivery_out_stock' => $row['delivery_out_stock'],
            ];
        }

        return array_values($products_formatted);
    }

    private function getSupplierAddresses()
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('supplier');
        $suppliers = Db::getInstance()->executeS($sql);

        $addresses = [];
        foreach ($suppliers as $supplier) {
            $addresses[$supplier['id_supplier']] = Db::getInstance()->getRow('SELECT * FROM `' . _DB_PREFIX_ . 'address` WHERE `id_supplier` = ' . (int)$supplier['id_supplier']);
        }
        return $addresses;
    }

    private function getRawSupplierData()
    {
        $data = [];
        $tables = [
            'supplier',
            'supplier_lang',
            'supplier_shop',
        ];

        foreach ($tables as $table) {
            $data[$table] = Db::getInstance()->executeS(
                'SELECT * FROM `' . _DB_PREFIX_ . $table . '`'
            );
        }

        return $data;
    }

    /**
     * Export products with combinations (attributes) including attribute groups/values codes and IDs.
     * Shape per item:
     * {
     *   id_product,
     *   reference,
     *   combinations: [
     *     {
     *       id_product_attribute,
     *       reference,
     *       ean13,
     *       isbn,
     *       upc,
     *       mpn,
     *       price, price_tax_incl (if present),
     *       attributes: [
     *          { id_attribute_group, id_attribute, group_name, attr_name }
     *       ]
     *     }
     *   ]
     * }
     *
     * @return array
     */
    private function exportProductCombinations()
    {
        $db = Db::getInstance();
        $idLang = (int)Context::getContext()->language->id;

        // Get all products having combinations
        $products = $db->executeS('SELECT DISTINCT p.id_product, p.reference
            FROM `' . _DB_PREFIX_ . 'product` p
            INNER JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON (pa.id_product = p.id_product)');
        if (!is_array($products) || empty($products)) {
            return [];
        }

        // Get all combinations with core fields
        $pas = $db->executeS('SELECT pa.id_product_attribute, pa.id_product, pa.reference, pa.ean13, pa.isbn, pa.upc, pa.mpn, pa.price
            FROM `' . _DB_PREFIX_ . 'product_attribute` pa');
        $byProduct = [];
        if (is_array($pas)) {
            foreach ($pas as $row) {
                $byProduct[(int)$row['id_product']][] = $row;
            }
        }

        // Map attributes for each combination
        $paIds = [];
        foreach ($pas as $row) { $paIds[(int)$row['id_product_attribute']] = true; }
        $paIds = array_keys($paIds);

        $attrMap = [];
        if (!empty($paIds)) {
            $in = implode(',', array_map('intval', $paIds));
            $attrRows = $db->executeS(
                'SELECT pac.id_product_attribute, ag.id_attribute_group, a.id_attribute,
                        agl.name AS group_name, al.name AS attr_name
                 FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac
                 INNER JOIN `' . _DB_PREFIX_ . 'attribute` a ON (a.id_attribute = pac.id_attribute)
                 INNER JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON (al.id_attribute = a.id_attribute AND al.id_lang = ' . (int)$idLang . ')
                 INNER JOIN `' . _DB_PREFIX_ . 'attribute_group` ag ON (ag.id_attribute_group = a.id_attribute_group)
                 INNER JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON (agl.id_attribute_group = ag.id_attribute_group AND agl.id_lang = ' . (int)$idLang . ')
                 WHERE pac.id_product_attribute IN (' . $in . ')'
            );
            if (is_array($attrRows)) {
                foreach ($attrRows as $r) {
                    $ipa = (int)$r['id_product_attribute'];
                    $attrMap[$ipa][] = [
                        'id_attribute_group' => (int)$r['id_attribute_group'],
                        'id_attribute' => (int)$r['id_attribute'],
                        'group_name' => (string)$r['group_name'],
                        'attr_name' => (string)$r['attr_name'],
                    ];
                }
            }
        }

        $export = [];
        foreach ($products as $p) {
            $idp = (int)$p['id_product'];
            $item = [
                'id_product' => $idp,
                'reference' => (string)$p['reference'],
                'combinations' => [],
            ];
            if (!empty($byProduct[$idp])) {
                foreach ($byProduct[$idp] as $pa) {
                    $ipa = (int)$pa['id_product_attribute'];
                    // Build a name-based signature and human label from attribute names
                    $names = [];
                    if (isset($attrMap[$ipa]) && is_array($attrMap[$ipa])) {
                        foreach ($attrMap[$ipa] as $a) {
                            $names[] = (string)$a['attr_name'];
                        }
                    }
                    $namesSorted = $names;
                    sort($namesSorted, SORT_NATURAL | SORT_FLAG_CASE);
                    $sigNames = implode('|', array_map(function($s){ return Tools::strtolower(trim((string)$s)); }, $namesSorted));
                    $label = implode(' - ', $namesSorted);
                    $item['combinations'][] = [
                        'id_product_attribute' => $ipa,
                        'reference' => (string)$pa['reference'],
                        'ean13' => (string)$pa['ean13'],
                        'isbn' => (string)$pa['isbn'],
                        'upc' => (string)$pa['upc'],
                        'mpn' => (string)$pa['mpn'],
                        'price' => (float)$pa['price'],
                        'attributes' => isset($attrMap[$ipa]) ? $attrMap[$ipa] : [],
                        'label' => $label,
                        'sig_names' => $sigNames,
                    ];
                }
            }
            $export[] = $item;
        }

        return $export;
    }

    /**
     * Export product features with multilingual names/values.
     * Shape per row:
     *   {
     *     id_product: int,
     *     id_feature: int,
     *     id_feature_value: int,
     *     custom: 0|1,
     *     feature: { iso => name },
     *     value: { iso => value }
     *   }
     *
     * @return array<int, array<string, mixed>>
     */
    private function exportProductFeatures()
    {
        $db = Db::getInstance();

        // Active languages
        $languages = $db->executeS('SELECT id_lang, iso_code FROM `' . _DB_PREFIX_ . 'lang` WHERE active = 1');
        if (empty($languages)) {
            return [];
        }

        // All product-feature assignments
        $fpRows = $db->executeS(
            'SELECT fp.id_product, fp.id_feature, fp.id_feature_value, fv.custom
             FROM `' . _DB_PREFIX_ . 'feature_product` fp
             INNER JOIN `' . _DB_PREFIX_ . 'feature_value` fv ON fv.id_feature_value = fp.id_feature_value'
        );

        if (empty($fpRows)) {
            return [];
        }

        // Collect ids to prefetch names/values
        $featureIds = [];
        $featureValueIds = [];
        foreach ($fpRows as $r) {
            $featureIds[(int)$r['id_feature']] = true;
            $featureValueIds[(int)$r['id_feature_value']] = true;
        }
        $featureIds = array_keys($featureIds);
        $featureValueIds = array_keys($featureValueIds);

        $featureNames = [];
        if (!empty($featureIds)) {
            $in = implode(',', array_map('intval', $featureIds));
            $flAll = $db->executeS('SELECT id_feature, id_lang, name FROM `' . _DB_PREFIX_ . 'feature_lang` WHERE id_feature IN (' . $in . ')');
            if (is_array($flAll)) {
                // Map id_lang -> iso
                $isoByLang = [];
                foreach ($languages as $l) {
                    $isoByLang[(int)$l['id_lang']] = (string)$l['iso_code'];
                }
                foreach ($flAll as $row) {
                    $idLang = (int)$row['id_lang'];
                    if (!isset($isoByLang[$idLang])) {
                        continue;
                    }
                    $iso = $isoByLang[$idLang];
                    $featureNames[(int)$row['id_feature']][$iso] = (string)$row['name'];
                }
            }
        }

        $valueTexts = [];
        if (!empty($featureValueIds)) {
            $in = implode(',', array_map('intval', $featureValueIds));
            $fvlAll = $db->executeS('SELECT id_feature_value, id_lang, value FROM `' . _DB_PREFIX_ . 'feature_value_lang` WHERE id_feature_value IN (' . $in . ')');
            if (is_array($fvlAll)) {
                $isoByLang = [];
                foreach ($languages as $l) {
                    $isoByLang[(int)$l['id_lang']] = (string)$l['iso_code'];
                }
                foreach ($fvlAll as $row) {
                    $idLang = (int)$row['id_lang'];
                    if (!isset($isoByLang[$idLang])) {
                        continue;
                    }
                    $iso = $isoByLang[$idLang];
                    $valueTexts[(int)$row['id_feature_value']][$iso] = (string)$row['value'];
                }
            }
        }

        $export = [];
        foreach ($fpRows as $row) {
            $fid = (int)$row['id_feature'];
            $fvid = (int)$row['id_feature_value'];
            $export[] = [
                'id_product' => (int)$row['id_product'],
                'id_feature' => $fid,
                'id_feature_value' => $fvid,
                'custom' => (int)$row['custom'],
                'feature' => isset($featureNames[$fid]) ? $featureNames[$fid] : new stdClass(),
                'value' => isset($valueTexts[$fvid]) ? $valueTexts[$fvid] : new stdClass(),
            ];
        }

        return $export;
    }

    /**
     * Export stock rows from stock_available to mirror exactly on target shop.
     * Each row contains: id_product, id_product_attribute, id_shop, id_shop_group, quantity, depends_on_stock, out_of_stock.
     *
     * @return array<int, array<string, int>>
     */
    private function exportProductStock()
    {
        $db = Db::getInstance();
        $hasReserved = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'stock_available` LIKE "reserved_quantity"'));
        $hasAvailableDate = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'stock_available` LIKE "available_date"'));

        $select = 'SELECT sa.id_product,
                           sa.id_product_attribute,
                           sa.id_shop,
                           sa.id_shop_group,
                           sa.quantity,
                           sa.depends_on_stock,
                           sa.out_of_stock';
        $select .= $hasReserved ? ', sa.reserved_quantity' : ', 0 AS reserved_quantity';
        $select .= $hasAvailableDate ? ', sa.available_date' : ', NULL AS available_date';
        $select .= ' FROM `' . _DB_PREFIX_ . 'stock_available` sa';

        $rows = $db->executeS($select);

        if (!is_array($rows) || empty($rows)) {
            return [];
        }

        $export = [];
        foreach ($rows as $r) {
            $export[] = [
                'id_product' => (int)$r['id_product'],
                'id_product_attribute' => (int)$r['id_product_attribute'],
                'id_shop' => isset($r['id_shop']) ? (int)$r['id_shop'] : 0,
                'id_shop_group' => isset($r['id_shop_group']) ? (int)$r['id_shop_group'] : 0,
                'quantity' => (int)$r['quantity'],
                'depends_on_stock' => (int)$r['depends_on_stock'],
                'out_of_stock' => (int)$r['out_of_stock'],
                'reserved_quantity' => isset($r['reserved_quantity']) ? (int)$r['reserved_quantity'] : 0,
                'available_date' => isset($r['available_date']) ? (string)$r['available_date'] : null,
            ];
        }

        return $export;
    }

    /**
     * Export all products with their deleted flag.
     * Shape: [{ id_product, deleted }]
     * @return array<int, array<string, int>>
     */
    private function exportProductsDeleted()
    {
        $db = Db::getInstance();
        // For testing: fill with product ids to filter, e.g. [123, 456]
        $testIds = [];
        $idsParam = Tools::getValue('ids');
        if (is_string($idsParam) && trim($idsParam) !== '') {
            $parts = preg_split('/[;,\s]+/', $idsParam);
            $tmp = [];
            foreach ((array)$parts as $p) { $v = (int)$p; if ($v > 0) { $tmp[$v] = true; } }
            if (!empty($tmp)) { $testIds = array_keys($tmp); }
        }
        $hasDeleted = !empty($db->executeS('SHOW COLUMNS FROM `' . _DB_PREFIX_ . 'product` LIKE "deleted"'));
        $select = 'SELECT p.id_product';
        $select .= $hasDeleted ? ', p.deleted' : ', 0 AS deleted';
        $from = ' FROM `' . _DB_PREFIX_ . 'product` p';

        $where = '';
        if (!empty($testIds)) {
            $where = ' WHERE p.id_product IN (' . implode(',', array_map('intval', $testIds)) . ')';
        }

        $rows = $db->executeS($select . $from . $where);
        if (!is_array($rows)) { return []; }
        $out = [];
        foreach ($rows as $r) {
            $out[] = [
                'id_product' => (int)$r['id_product'],
                'deleted' => (int)$r['deleted'],
            ];
        }
        return $out;
    }
}