{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "210810ab5edf00d78487181d46da6bca", "packages": [{"name": "composer/package-versions-deprecated", "version": "1.11.99", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855", "reference": "c8c9aa8a14cc3d3bec86d0a8c3fa52ea79936855", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-08-25T05:50:16+00:00"}, {"name": "jetbrains/phpstorm-stubs", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/JetBrains/phpstorm-stubs.git", "reference": "5b7def27af1f88c009f137b4c4a38bb4732a3713"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JetBrains/phpstorm-stubs/zipball/5b7def27af1f88c009f137b4c4a38bb4732a3713", "reference": "5b7def27af1f88c009f137b4c4a38bb4732a3713", "shasum": ""}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "nikic/php-parser": "^4", "php": "^7.4", "phpdocumentor/reflection-docblock": "dev-master", "phpunit/phpunit": "^9"}, "type": "library", "autoload": {"files": ["PhpStormStubsMap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "PHP runtime & extensions header files for PhpStorm", "homepage": "https://www.jetbrains.com/phpstorm", "keywords": ["autocomplete", "code", "inference", "inspection", "jetbrains", "phpstorm", "stubs", "type"], "time": "2020-09-14T12:37:46+00:00"}, {"name": "nikic/php-parser", "version": "v4.10.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "658f1be311a230e0907f5dfe0213742aff0596de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/658f1be311a230e0907f5dfe0213742aff0596de", "reference": "658f1be311a230e0907f5dfe0213742aff0596de", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-09-26T10:30:38+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "symfony/console", "version": "v4.4.16", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "20f73dd143a5815d475e0838ff867bce1eebd9d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/20f73dd143a5815d475e0838ff867bce1eebd9d5", "reference": "20f73dd143a5815d475e0838ff867bce1eebd9d5", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T11:50:19+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.16", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "e74b873395b7213d44d1397bd4a605cd1632a68a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/e74b873395b7213d44d1397bd4a605cd1632a68a", "reference": "e74b873395b7213d44d1397bd4a605cd1632a68a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T11:50:19+00:00"}, {"name": "symfony/finder", "version": "v4.4.16", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "26f63b8d4e92f2eecd90f6791a563ebb001abe31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/26f63b8d4e92f2eecd90f6791a563ebb001abe31", "reference": "26f63b8d4e92f2eecd90f6791a563ebb001abe31", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T11:50:19+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "f4ba089a5b6366e453971d3aad5fe8e897b37f41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/f4ba089a5b6366e453971d3aad5fe8e897b37f41", "reference": "f4ba089a5b6366e453971d3aad5fe8e897b37f41", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "39d483bdf39be819deabf04ec872eb0b2410b531"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/39d483bdf39be819deabf04ec872eb0b2410b531", "reference": "39d483bdf39be819deabf04ec872eb0b2410b531", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "8ff431c517be11c78c48a39a66d37431e26a6bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/8ff431c517be11c78c48a39a66d37431e26a6bed", "reference": "8ff431c517be11c78c48a39a66d37431e26a6bed", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "e70aa8b064c5b72d3df2abd5ab1e90464ad009de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/e70aa8b064c5b72d3df2abd5ab1e90464ad009de", "reference": "e70aa8b064c5b72d3df2abd5ab1e90464ad009de", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/b776d18b303a39f56c63747bcb977ad4b27aca26", "reference": "b776d18b303a39f56c63747bcb977ad4b27aca26", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-07-06T13:19:58+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "f220a51458bf4dd0dedebb171ac3457813c72bbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/f220a51458bf4dd0dedebb171ac3457813c72bbc", "reference": "f220a51458bf4dd0dedebb171ac3457813c72bbc", "shasum": ""}, "require": {"php": ">=7"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6.0.9 | ^7", "psalm/phar": "^3.11@dev", "react/promise": "^2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Amp\\": "lib"}, "files": ["lib/functions.php", "lib/Internal/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "http://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2020-07-14T21:47:18+00:00"}, {"name": "amphp/byte-stream", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "f0c20cf598a958ba2aa8c6e5a71c697d652c7088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/f0c20cf598a958ba2aa8c6e5a71c697d652c7088", "reference": "f0c20cf598a958ba2aa8c6e5a71c697d652c7088", "shasum": ""}, "require": {"amphp/amp": "^2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.4", "friendsofphp/php-cs-fixer": "^2.3", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^6 || ^7 || ^8", "psalm/phar": "^3.11.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Amp\\ByteStream\\": "lib"}, "files": ["lib/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "http://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "time": "2020-06-29T18:35:05+00:00"}, {"name": "amphp/parallel", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/amphp/parallel.git", "reference": "2c1039bf7ca137eae4d954b14c09a7535d7d4e1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parallel/zipball/2c1039bf7ca137eae4d954b14c09a7535d7d4e1c", "reference": "2c1039bf7ca137eae4d954b14c09a7535d7d4e1c", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.6.1", "amphp/parser": "^1", "amphp/process": "^1", "amphp/serialization": "^1", "amphp/sync": "^1.0.1", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.1", "phpunit/phpunit": "^8 || ^7"}, "type": "library", "autoload": {"psr-4": {"Amp\\Parallel\\": "lib"}, "files": ["lib/Context/functions.php", "lib/Sync/functions.php", "lib/Worker/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parallel processing component for Amp.", "homepage": "https://github.com/amphp/parallel", "keywords": ["async", "asynchronous", "concurrent", "multi-processing", "multi-threading"], "time": "2020-04-27T15:12:37+00:00"}, {"name": "amphp/parallel-functions", "version": "v0.1.3", "source": {"type": "git", "url": "https://github.com/amphp/parallel-functions.git", "reference": "12e6c602e067b02f78ddf5b720c17e9aa01ad4b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parallel-functions/zipball/12e6c602e067b02f78ddf5b720c17e9aa01ad4b4", "reference": "12e6c602e067b02f78ddf5b720c17e9aa01ad4b4", "shasum": ""}, "require": {"amphp/amp": "^2.0.3", "amphp/parallel": "^0.1.8 || ^0.2 || ^1", "opis/closure": "^3.0.7", "php": ">=7"}, "require-dev": {"amphp/phpunit-util": "^1.0", "friendsofphp/php-cs-fixer": "^2.9", "phpunit/phpunit": "^6.5"}, "type": "library", "autoload": {"psr-4": {"Amp\\ParallelFunctions\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Parallel processing made simple.", "time": "2018-10-28T15:29:02+00:00"}, {"name": "amphp/parser", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/amphp/parser.git", "reference": "f83e68f03d5b8e8e0365b8792985a7f341c57ae1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parser/zipball/f83e68f03d5b8e8e0365b8792985a7f341c57ae1", "reference": "f83e68f03d5b8e8e0365b8792985a7f341c57ae1", "shasum": ""}, "require": {"php": ">=7"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.3", "phpunit/phpunit": "^6"}, "type": "library", "autoload": {"psr-4": {"Amp\\Parser\\": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A generator parser to make streaming parsers simple.", "homepage": "https://github.com/amphp/parser", "keywords": ["async", "non-blocking", "parser", "stream"], "time": "2017-06-06T05:29:10+00:00"}, {"name": "amphp/process", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/amphp/process.git", "reference": "355b1e561b01c16ab3d78fada1ad47ccc96df70e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/process/zipball/355b1e561b01c16ab3d78fada1ad47ccc96df70e", "reference": "355b1e561b01c16ab3d78fada1ad47ccc96df70e", "shasum": ""}, "require": {"amphp/amp": "^2", "amphp/byte-stream": "^1.4", "php": ">=7"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1", "phpunit/phpunit": "^6"}, "type": "library", "autoload": {"psr-4": {"Amp\\Process\\": "lib"}, "files": ["lib/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Asynchronous process manager.", "homepage": "https://github.com/amphp/process", "time": "2019-02-26T16:33:03+00:00"}, {"name": "amphp/serialization", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/amphp/serialization.git", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/serialization/zipball/693e77b2fb0b266c3c7d622317f881de44ae94a1", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "autoload": {"psr-4": {"Amp\\Serialization\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Serialization tools for IPC and data storage in PHP.", "homepage": "https://github.com/amphp/serialization", "keywords": ["async", "asynchronous", "serialization", "serialize"], "time": "2020-03-25T21:39:07+00:00"}, {"name": "amphp/sync", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/amphp/sync.git", "reference": "613047ac54c025aa800a9cde5b05c3add7327ed4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/sync/zipball/613047ac54c025aa800a9cde5b05c3add7327ed4", "reference": "613047ac54c025aa800a9cde5b05c3add7327ed4", "shasum": ""}, "require": {"amphp/amp": "^2.2", "php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "amphp/phpunit-util": "^1.1", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "autoload": {"psr-4": {"Amp\\Sync\\": "src"}, "files": ["src/functions.php", "src/ConcurrentIterator/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mutex, Semaphore, and other synchronization tools for Amp.", "homepage": "https://github.com/amphp/sync", "keywords": ["async", "asynchronous", "mutex", "semaphore", "synchronization"], "time": "2020-05-07T18:57:50+00:00"}, {"name": "bamarni/composer-bin-plugin", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/bamarni/composer-bin-plugin.git", "reference": "9329fb0fbe29e0e1b2db8f4639a193e4f5406225"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bamarni/composer-bin-plugin/zipball/9329fb0fbe29e0e1b2db8f4639a193e4f5406225", "reference": "9329fb0fbe29e0e1b2db8f4639a193e4f5406225", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "symfony/console": "^2.5 || ^3.0 || ^4.0"}, "type": "composer-plugin", "extra": {"class": "Bamarni\\Composer\\Bin\\Plugin"}, "autoload": {"psr-4": {"Bamarni\\Composer\\Bin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "No conflicts for your bin dependencies", "keywords": ["composer", "conflict", "dependency", "executable", "isolation", "tool"], "time": "2020-05-03T08:27:20+00:00"}, {"name": "beber<PERSON>i/assert", "version": "v3.2.7", "source": {"type": "git", "url": "https://github.com/beberlei/assert.git", "reference": "d63a6943fc4fd1a2aedb65994e3548715105abcf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/assert/zipball/d63a6943fc4fd1a2aedb65994e3548715105abcf", "reference": "d63a6943fc4fd1a2aedb65994e3548715105abcf", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "php": "^7"}, "require-dev": {"friendsofphp/php-cs-fixer": "*", "phpstan/phpstan-shim": "*", "phpunit/phpunit": ">=6.0.0 <8"}, "suggest": {"ext-intl": "Needed to allow Assertion::count(), Assertion::isCountable(), Assertion::minCount(), and Assertion::maxCount() to operate on ResourceBundles"}, "type": "library", "autoload": {"psr-4": {"Assert\\": "lib/Assert"}, "files": ["lib/Assert/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Collaborator"}], "description": "Thin assertion library for input validation in business models.", "keywords": ["assert", "assertion", "validation"], "time": "2019-12-19T17:51:41+00:00"}, {"name": "composer/semver", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2020-01-13T12:06:48+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-06-04T11:16:35+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/f350df0268e904597e3bd9c4685c53e0e333feea", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-05-29T17:27:14+00:00"}, {"name": "hoa/compiler", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Compiler.git", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Compiler/zipball/aa09caf0bf28adae6654ca6ee415ee2f522672de", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/file": "~1.0", "hoa/iterator": "~2.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/regex": "~1.0", "hoa/visitor": "~2.0"}, "require-dev": {"hoa/json": "~2.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Compiler\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Compiler library.", "homepage": "https://hoa-project.net/", "keywords": ["algebraic", "ast", "compiler", "context-free", "coverage", "exhaustive", "grammar", "isotropic", "language", "lexer", "library", "ll1", "llk", "parser", "pp", "random", "regular", "rule", "sampler", "syntax", "token", "trace", "uniform"], "time": "2017-08-08T07:44:07+00:00"}, {"name": "hoa/consistency", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Consistency.git", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Consistency/zipball/fd7d0adc82410507f332516faf655b6ed22e4c2f", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f", "shasum": ""}, "require": {"hoa/exception": "~1.0", "php": ">=5.5.0"}, "require-dev": {"hoa/stream": "~1.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Consistency\\": "."}, "files": ["Prelude.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Consistency library.", "homepage": "https://hoa-project.net/", "keywords": ["autoloader", "callable", "consistency", "entity", "flex", "keyword", "library"], "time": "2017-05-02T12:18:12+00:00"}, {"name": "hoa/event", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Event.git", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Event/zipball/6c0060dced212ffa3af0e34bb46624f990b29c54", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Event\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Event library.", "homepage": "https://hoa-project.net/", "keywords": ["event", "library", "listener", "observer"], "time": "2017-01-13T15:30:50+00:00"}, {"name": "hoa/exception", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Exception.git", "reference": "091727d46420a3d7468ef0595651488bfc3a458f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Exception/zipball/091727d46420a3d7468ef0595651488bfc3a458f", "reference": "091727d46420a3d7468ef0595651488bfc3a458f", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Exception\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Exception library.", "homepage": "https://hoa-project.net/", "keywords": ["exception", "library"], "time": "2017-01-16T07:53:27+00:00"}, {"name": "hoa/file", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/File.git", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/File/zipball/35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/stream": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\File\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\File library.", "homepage": "https://hoa-project.net/", "keywords": ["Socket", "directory", "file", "finder", "library", "link", "temporary"], "time": "2017-07-11T07:42:15+00:00"}, {"name": "hoa/iterator", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Iterator.git", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Iterator/zipball/d1120ba09cb4ccd049c86d10058ab94af245f0cc", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Iterator\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Iterator library.", "homepage": "https://hoa-project.net/", "keywords": ["iterator", "library"], "time": "2017-01-10T10:34:47+00:00"}, {"name": "hoa/math", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Math.git", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Math/zipball/7150785d30f5d565704912116a462e9f5bc83a0c", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c", "shasum": ""}, "require": {"hoa/compiler": "~3.0", "hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/protocol": "~1.0", "hoa/zformat": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Math\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Math library.", "homepage": "https://hoa-project.net/", "keywords": ["arrangement", "combination", "combinatorics", "counting", "library", "math", "permutation", "sampler", "set"], "time": "2017-05-16T08:02:17+00:00"}, {"name": "hoa/protocol", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Protocol.git", "reference": "5c2cf972151c45f373230da170ea015deecf19e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Protocol/zipball/5c2cf972151c45f373230da170ea015deecf19e2", "reference": "5c2cf972151c45f373230da170ea015deecf19e2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Protocol\\": "."}, "files": ["Wrapper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Protocol library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "protocol", "resource", "stream", "wrapper"], "time": "2017-01-14T12:26:10+00:00"}, {"name": "hoa/regex", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Regex.git", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Regex/zipball/7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/ustring": "~4.0", "hoa/visitor": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Regex\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Regex library.", "homepage": "https://hoa-project.net/", "keywords": ["compiler", "library", "regex"], "time": "2017-01-13T16:10:24+00:00"}, {"name": "hoa/stream", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Stream.git", "reference": "3293cfffca2de10525df51436adf88a559151d82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Stream/zipball/3293cfffca2de10525df51436adf88a559151d82", "reference": "3293cfffca2de10525df51436adf88a559151d82", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/protocol": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Stream\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Stream library.", "homepage": "https://hoa-project.net/", "keywords": ["Context", "bucket", "composite", "filter", "in", "library", "out", "protocol", "stream", "wrapper"], "time": "2017-02-21T16:01:06+00:00"}, {"name": "hoa/ustring", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Ustring.git", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Ustring/zipball/e6326e2739178799b1fe3fdd92029f9517fa17a0", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "suggest": {"ext-iconv": "ext/iconv must be present (or a third implementation) to use Hoa\\Ustring::transcode().", "ext-intl": "To get a better Hoa\\Ustring::toAscii() and Hoa\\Ustring::compareTo()."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Ustring\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Ustring library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "search", "string", "unicode"], "time": "2017-01-16T07:08:25+00:00"}, {"name": "hoa/visitor", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Visitor.git", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Visitor/zipball/c18fe1cbac98ae449e0d56e87469103ba08f224a", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a", "shasum": ""}, "require": {"hoa/consistency": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Visitor\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Visitor library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "structure", "visit", "visitor"], "time": "2017-01-16T07:02:03+00:00"}, {"name": "hoa/zformat", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Zformat.git", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Zformat/zipball/522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Zformat\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Zformat library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "parameter", "zformat"], "time": "2017-01-10T10:39:54+00:00"}, {"name": "humbug/box", "version": "3.8.5", "source": {"type": "git", "url": "https://github.com/humbug/box.git", "reference": "e708d75da59b01ce9d59ce519c55bb7cfcfa6fe5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/humbug/box/zipball/e708d75da59b01ce9d59ce519c55bb7cfcfa6fe5", "reference": "e708d75da59b01ce9d59ce519c55bb7cfcfa6fe5", "shasum": ""}, "require": {"amphp/parallel-functions": "^0.1.3", "beberlei/assert": "^3.2", "composer/semver": "^1.5", "composer/xdebug-handler": "^1.3.2", "ext-phar": "*", "hoa/compiler": "^3.17", "humbug/php-scoper": "^0.13", "justinrainbow/json-schema": "^5.2.9", "nikic/iter": "^2.0", "nikic/php-parser": "^4.2", "ocramius/package-versions": "^1.4", "opis/closure": "^3.2", "paragonie/pharaoh": "^0.5", "php": "^7.2", "phpseclib/phpseclib": "^2.0", "psr/log": "^1.0", "seld/jsonlint": "^1.7", "symfony/console": "^4.3.5", "symfony/filesystem": "^4.2", "symfony/finder": "^4.0", "symfony/process": "^4.2", "symfony/var-dumper": "^4.2", "webmozart/path-util": "^2.3"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.3", "infection/infection": "^0.10", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^8.1", "symfony/phpunit-bridge": "^4.2 || ^5.0"}, "suggest": {"ext-openssl": "To accelerate private key generation."}, "bin": ["bin/box"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "bamarni-bin": {"bin-links": false}}, "autoload": {"psr-4": {"KevinGH\\Box\\": "src"}, "files": ["src/FileSystem/file_system.php", "src/consts.php", "src/functions.php"], "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast, zero config application bundler with PHARs.", "keywords": ["phar"], "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2020-07-28T07:16:37+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.10", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2020-05-27T16:41:55+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2020-06-29T13:22:24+00:00"}, {"name": "nikic/iter", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/nikic/iter.git", "reference": "1417341030a43fba0764486f7ad49ccc7b76e73f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/iter/zipball/1417341030a43fba0764486f7ad49ccc7b76e73f", "reference": "1417341030a43fba0764486f7ad49ccc7b76e73f", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "~7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"files": ["src/iter.func.php", "src/iter.php", "src/iter.rewindable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Iteration primitives using generators", "keywords": ["functional", "generator", "iterator"], "time": "2019-05-12T20:50:16+00:00"}, {"name": "opis/closure", "version": "3.5.5", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "dec9fc5ecfca93f45cd6121f8e6f14457dff372c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/dec9fc5ecfca93f45cd6121f8e6f14457dff372c", "reference": "dec9fc5ecfca93f45cd6121f8e6f14457dff372c", "shasum": ""}, "require": {"php": "^5.4 || ^7.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2020-06-17T14:59:55+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2", "reference": "47a1cedd2e4d52688eb8c96469c05ebc8fd28fa2", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7", "vimeo/psalm": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2019-11-06T19:20:29+00:00"}, {"name": "paragonie/pharaoh", "version": "v0.5.0", "source": {"type": "git", "url": "https://github.com/paragonie/pharaoh.git", "reference": "060418e946de2f39a3618ad70d9b6d0a61437b83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/pharaoh/zipball/060418e946de2f39a3618ad70d9b6d0a61437b83", "reference": "060418e946de2f39a3618ad70d9b6d0a61437b83", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^2", "paragonie/sodium_compat": "^1.3", "php": "^7", "ulrichsg/getopt-php": "^3"}, "require-dev": {"vimeo/psalm": "^1|^2"}, "bin": ["pharaoh"], "type": "library", "autoload": {"psr-4": {"ParagonIE\\Pharaoh\\": "src/<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Developer"}], "description": "Compare PHARs from the Command Line", "keywords": ["auditing", "diff", "phar", "security", "tool", "utility"], "time": "2018-11-02T16:45:56+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "paragonie/sodium_compat", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "bbade402cbe84c69b718120911506a3aa2bae653"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/bbade402cbe84c69b718120911506a3aa2bae653", "reference": "bbade402cbe84c69b718120911506a3aa2bae653", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "php": "^5.2.4|^5.3|^5.4|^5.5|^5.6|^7|^8"}, "require-dev": {"phpunit/phpunit": "^3|^4|^5|^6|^7"}, "suggest": {"ext-libsodium": "PHP < 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security.", "ext-sodium": "PHP >= 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "type": "library", "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "time": "2020-03-20T21:48:09+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "3170448f5769fe19f456173d833734e0ff1b84df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/3170448f5769fe19f456173d833734e0ff1b84df", "reference": "3170448f5769fe19f456173d833734e0ff1b84df", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2020-07-20T20:05:34+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e878a14a65245fbe78f8080eba03b47c3b705651", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2020-06-27T10:12:23+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.28", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d1ca58cf33cb21046d702ae3a7b14fdacd9f3260", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2020-07-08T09:08:33+00:00"}, {"name": "phpspec/prophecy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/b20034be5efcdab4fb60ca3a29cba2949aead160", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2", "phpdocumentor/reflection-docblock": "^5.0", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-07-08T12:44:21+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.10", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/f1884187926fbb755a9aaf0b3836ad3165b478bf", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2019-11-20T13:55:58+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-token-stream/zipball/995192df77f63a59e47f025390d2d1fdf8f425ff", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "34c18baa6a44f1d1fbf0338907139e9dce95b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/34c18baa6a44f1d1fbf0338907139e9dce95b997", "reference": "34c18baa6a44f1d1fbf0338907139e9dce95b997", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.9.1", "phar-io/manifest": "^1.0.3", "phar-io/version": "^2.0.1", "php": "^7.2", "phpspec/prophecy": "^1.8.1", "phpunit/php-code-coverage": "^7.0.7", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.2", "sebastian/exporter": "^3.1.1", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "funding": [{"url": "https://phpunit.de/donate.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-06-22T07:06:58+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "shasum": ""}, "require": {"php": "^7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2019-02-01T05:30:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": ""}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/type", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/3aaaa15fa71d27650d62a948be022fe3b48541a3", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "time": "2019-07-02T08:10:15+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2020-04-30T19:05:18+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "cede45fcdfabdd6043b3592e83678e42ec69e930"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/cede45fcdfabdd6043b3592e83678e42ec69e930", "reference": "cede45fcdfabdd6043b3592e83678e42ec69e930", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/process", "version": "v4.4.16", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "2f4b049fb80ca5e9874615a2a85dc2a502090f05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/2f4b049fb80ca5e9874615a2a85dc2a502090f05", "reference": "2f4b049fb80ca5e9874615a2a85dc2a502090f05", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-24T11:50:19+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.16", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "3718e18b68d955348ad860e505991802c09f5f73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/3718e18b68d955348ad860e505991802c09f5f73", "reference": "3718e18b68d955348ad860e505991802c09f5f73", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-26T20:47:51+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2020-07-12T23:59:07+00:00"}, {"name": "ulrichsg/getopt-php", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/getopt-php/getopt-php.git", "reference": "9121d7c2c51a6a59ee407c49a13b4d8cfae71075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getopt-php/getopt-php/zipball/9121d7c2c51a6a59ee407c49a13b4d8cfae71075", "reference": "9121d7c2c51a6a59ee407c49a13b4d8cfae71075", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "autoload": {"psr-4": {"GetOpt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Command line arguments parser for PHP 5.4 - 7.3", "homepage": "http://getopt-php.github.io/getopt-php", "time": "2020-07-14T06:09:04+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-07-08T17:02:28+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "time": "2015-12-17T08:42:14+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"jetbrains/phpstorm-stubs": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^7.2"}, "platform-dev": [], "platform-overrides": {"php": "7.2.0"}, "plugin-api-version": "1.1.0"}