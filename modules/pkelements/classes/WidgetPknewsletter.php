<?php

/**
 * Promokit Widgets
 *
 * @package   Promokit
 * @version   1.0.0
 * <AUTHOR>
 * @copyright Copyright Ⓒ Since 2011 promokit.eu <@email:<EMAIL>>
 * @license   You only can use the module, nothing more!
 */

namespace CE;

defined('_PS_VERSION_') or die;

use Tools;
use Module;
use Context;
use Promokit\Module\Pkelements\WidgetCache;
use Promokit\Module\Pkelements\WidgetHelper;
use Promokit\Module\Pkelements\WidgetViewBase;
use Promokit\Module\Pkelements\WidgetTemplate;

class WidgetPknewsletter extends WidgetViewBase
{
    public $module_name = 'ps_emailsubscription';

    public function getName()
    {
        return 'pknewsletter';
    }

    public function getTitle()
    {
        return WidgetHelper::getTrans()->trans('Newsletter', [], 'Modules.Pkelements.Admin');
    }

    public function getIcon()
    {
        return 'fa fa-paper-plane-o';
    }

    public function getCategories()
    {
        return ['promokit'];
    }

    protected function _registerControls()
    {
        $this->addTitleControls('Stay up to date');

        $this->startControlsSection(
            'section_title',
            ['label' => WidgetHelper::getTrans()->trans('General', [], 'Admin.Global')]
        );

        $this->addResponsiveControl(
            'content_align',
            [
                'label' => WidgetHelper::getTrans()->trans('Content Direction', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::CHOOSE,
                'options' => [
                    'row' => [
                        'title' => WidgetHelper::getTrans()->trans('Row', [], 'Modules.Pkelements.Admin'),
                        'icon' => 'fa fa-ellipsis-h',
                    ],
                    'column' => [
                        'title' => WidgetHelper::getTrans()->trans('Column', [], 'Modules.Pkelements.Admin'),
                        'icon' => 'fa fa-ellipsis-v',
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .pk-ce-widget-wrapper' => 'display:flex;align-items:center;flex-direction: {{VALUE}}',
                ],
                'default' => 'column'
            ]
        );

        $this->addResponsiveControl(
            'text_align',
            [
                'label' => WidgetHelper::getTrans()->trans('Alignment', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => WidgetHelper::getTrans()->trans('Left', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-left',
                    ],
                    'center' => [
                        'title' => WidgetHelper::getTrans()->trans('Center', [], 'Modules.Pkelements.Admin'),
                        'icon' => 'fa fa-align-center',
                    ],
                    'right' => [
                        'title' => WidgetHelper::getTrans()->trans('Right', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-right',
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .newsletter-widget' => 'text-align: {{VALUE}}',
                    '{{WRAPPER}} .newsletter-form' => 'text-align: {{VALUE}};display:block',
                    '{{WRAPPER}} .module-title' => 'text-align: {{VALUE}}',
                    '{{WRAPPER}} .pknlinput-wrap' => 'display:inline-block',
                    '{{WRAPPER}} .pk-ce-widget-wrapper' => 'align-items: {{VALUE}}',
                ],
                'default' => 'left',
                'condition' => ['content_align' => 'column'],
            ]
        );

        $this->addResponsiveControl(
            'text_align2',
            [
                'label' => WidgetHelper::getTrans()->trans('Alignment', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::CHOOSE,
                'options' => [
                    'flex-start' => [
                        'title' => WidgetHelper::getTrans()->trans('Left', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-left',
                    ],
                    'center' => [
                        'title' => WidgetHelper::getTrans()->trans('Center', [], 'Modules.Pkelements.Admin'),
                        'icon' => 'fa fa-align-center',
                    ],
                    'flex-end' => [
                        'title' => WidgetHelper::getTrans()->trans('Right', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-right',
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .pk-ce-widget-wrapper' => 'justify-content: {{VALUE}}',
                ],
                'default' => 'center',
                'condition' => ['content_align' => 'row'],
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'info_message_title',
            ['label' => WidgetHelper::getTrans()->trans('Info Message', [], 'Modules.Pkelements.Admin')]
        );

        $this->addControl(
            'information',
            [
                'label'   => WidgetHelper::getTrans()->trans('Info message', [], 'Modules.Pkelements.Admin'),
                'type'    => ControlsManager::TEXTAREA,
                'default' => '(Durch anklicken des Kästchens aktivierst Du unseren Newsletter und stimmst unserer Datenschutzerklärung zu.
Du kannst in jedem Newsletter selbst deine Einwilligung jederzeit widerrufen und den Newsletter abbestellen)',
            ]
        );

        $this->addControl(
            'info_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Info message color', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .newsletter-info' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->addGroupControl(
            GroupControlTypography::getType(),
            [
                'name'     => 'info_typography',
                'label'    => WidgetHelper::getTrans()->trans('Text Typography', [], 'Modules.Pkelements.Admin'),
                'selector' => '{{WRAPPER}} .newsletter-info',
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'view_title',
            ['label' => WidgetHelper::getTrans()->trans('Input Field', [], 'Modules.Pkelements.Admin')]
        );

        $this->addControl(
            'input_text',
            [
                'label'       => WidgetHelper::getTrans()->trans('Input Text', [], 'Modules.Pkelements.Admin'),
                'default'     => WidgetHelper::getTrans()->trans('Your email address', [], 'Modules.Pkelements.Admin'),
                'type'        => ControlsManager::TEXT,
                'label_block' => true,
                'separator'   => 'after'
            ]
        );

        $this->addResponsiveControl(
            'form_width',
            [
                'type'    => ControlsManager::NUMBER,
                'label'   => WidgetHelper::getTrans()->trans('Input Width', [], 'Modules.Pkelements.Admin'),
                'min'   => 1,
                'default' => 300,
                'selectors' => [
                    '{{WRAPPER}} .pknlinput-wrap' => 'width: {{VALUE}}px',
                ],
            ]
        );

        $this->addControl(
            'input_padding',
            [
                'label'      => WidgetHelper::getTrans()->trans('Input Padding', [], 'Modules.Pkelements.Admin'),
                'type'       => ControlsManager::DIMENSIONS,
                'selectors'  => [
                    '{{WRAPPER}} .newsletter-input' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->addControl(
            'input_margin',
            [
                'label'      => WidgetHelper::getTrans()->trans('Input Margin', [], 'Modules.Pkelements.Admin'),
                'type'       => ControlsManager::DIMENSIONS,
                'selectors'  => [
                    '{{WRAPPER}} .pknlinput-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->addGroupControl(
            GroupControlBorder::getType(),
            [
                'name'     => 'input_border',
                'label'    => WidgetHelper::getTrans()->trans('Input Border', [], 'Modules.Pkelements.Admin'),
                'selector' => '{{WRAPPER}} .newsletter-input',
            ]
        );

        $this->addControl(
            'input_border_radius',
            [
                'label'      => WidgetHelper::getTrans()->trans('Input Border Radius', [], 'Modules.Pkelements.Admin'),
                'type'       => ControlsManager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors'  => [
                    '{{WRAPPER}} .newsletter-input' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->addControl(
            'input_text_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Input Text Color', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .newsletter-input' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .newsletter-input::placeholder' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .submit-widget-newsletter' => 'color: {{VALUE}};',
                ],
                'separator' => 'before'
            ]
        );

        $this->addGroupControl(
            GroupControlTypography::getType(),
            [
                'name'     => 'input_text_typography',
                'label'    => WidgetHelper::getTrans()->trans('Input Text Typography', [], 'Modules.Pkelements.Admin'),
                'selector' => '{{WRAPPER}} .newsletter-input, {{WRAPPER}} .newsletter-input::placeholder',
            ]
        );

        $this->addControl(
            'input_bg_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Input Background', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .newsletter-input' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'gdpr_title',
            ['label' => WidgetHelper::getTrans()->trans('GDPR Section', [], 'Modules.Pkelements.Admin')]
        );

        $this->addControl(
            'show_gdpr',
            [
                'type' => ControlsManager::SWITCHER,
                'label' => WidgetHelper::getTrans()->trans('GDPR checkbox', [], 'Modules.Pkelements.Admin'),
                'label_on' => WidgetHelper::getTrans()->trans('Show', [], 'Admin.Actions'),
                'label_off' => WidgetHelper::getTrans()->trans('Hide', [], 'Modules.Pkelements.Admin'),
                'default' => 'yes'
            ]
        );

        $this->addControl(
            'gdpr_color',
            [
                'label' => WidgetHelper::getTrans()->trans('GDPR Text Color', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .psgdpr_consent_message span:last-child' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->addGroupControl(
            GroupControlTypography::getType(),
            [
                'name'     => 'gdpr_typography',
                'label'    => WidgetHelper::getTrans()->trans('GDPR Text Typography', [], 'Modules.Pkelements.Admin'),
                'selector' => '{{WRAPPER}} .psgdpr_consent_message span:last-child',
            ]
        );

        $this->addControl(
            'gdpr_padding',
            [
                'label'      => WidgetHelper::getTrans()->trans('GDPR Padding', [], 'Modules.Pkelements.Admin'),
                'type'       => ControlsManager::DIMENSIONS,
                'selectors'  => [
                    '{{WRAPPER}} .psgdpr_consent_message' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->endControlsSection();
    }

    protected function render()
    {
        if (is_admin()) {
            return print '<div class="ce-remote-render"></div>';
        }

        if (!Module::isInstalled($this->module_name)) {
            echo '<div class="elementor-alert elementor-alert-danger">"Email Subscription" module is not installed</div>';
            return;
        }

        $widgetTemplate = new WidgetTemplate;
        $settings = $this->getSettings();

        $cache = WidgetCache::getInstance();
        $cache_id = WidgetCache::getCacheId($settings);
        $cached_data = $cache->getVar($cache_id);

        if ($cached_data === false || Tools::getIsset('render')) {
            $settings['ps_emailsubscription_id'] = Module::getModuleIdByName($this->module_name);
            $settings['params'] = WidgetHelper::getCarouselOptions($settings);

            Context::getContext()->smarty->assign($settings);
            $settings['title'] = $widgetTemplate->getHeading($settings['title']);
            $settings['content'] = $widgetTemplate->getTemplate($this->getName());

            $cache->setVar($cache_id, $settings);
        } else {
            $settings = $cached_data;
        }

        Context::getContext()->controller->registerJavascript(
            'pkelements-newsletter',
            //'modules/pkelements/views/js/newsletter.min.js',
            'modules/pkelements/views/js/newsletter.js',
            ['priority' => 598, 'attributes' => 'defer']
        );

        Context::getContext()->smarty->assign($settings);
        
        $widgetTemplate->echoWidget();
    }
}
