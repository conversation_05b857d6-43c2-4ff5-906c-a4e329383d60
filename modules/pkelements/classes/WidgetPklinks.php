<?php
/**
 * Promokit Widgets
 *
 * @package   Promokit
 * @version   1.0.0
 * <AUTHOR>
 * @copyright Copyright Ⓒ Since 2011 promokit.eu <@email:<EMAIL>>
 * @license   You only can use the module, nothing more!
 */

namespace CE;

defined('_PS_VERSION_') or die;

use Db;
use Hook;
use Tools;
use Module;
use Context;
use Promokit\Module\Pkelements\WidgetCache;
use Promokit\Module\Pkelements\WidgetHelper;
use Promokit\Module\Pkelements\WidgetViewBase;
use Promokit\Module\Pkelements\WidgetTemplate;
use PrestaShop\Module\LinkList\LegacyLinkBlockRepository;
use PrestaShop\Module\LinkList\Presenter\LinkBlockPresenter;

class WidgetPklinks extends WidgetViewBase
{
    public $module_name = 'ps_linklist';

    public function getName()
    {
        return 'pklinks';
    }

    public function getTitle()
    {
        return WidgetHelper::getTrans()->trans('Links', [], 'Modules.Pkelements.Admin');
    }

    public function getIcon()
    {
        return 'fa fa-link';
    }

    public function getCategories()
    {
        return ['promokit'];
    }

    public function getKeywords()
    {
        return ['header'];
    }

    protected function _registerControls()
    {
        $this->addTitleControls();

        $this->startControlsSection(
            'section_title',
            ['label' => WidgetHelper::getTrans()->trans('Widget Content', [], 'Modules.Pkelements.Admin')]
        );

        if (!\Module::isInstalled($this->module_name))
        {
            $this->addControl(
                'module_state_error',
                [
                    'type' => ControlsManager::RAW_HTML,
                    'raw' => 'Make sure the "Link List" module is installed',
                    'content_classes' => 'elementor-alert elementor-alert-danger',
                ]
            );

            $this->endControlsSection();

            return;
        }

        $this->addControl(
            'show_block_title',
            [
                'type' => ControlsManager::SWITCHER,
                'label' => WidgetHelper::getTrans()->trans('Block Title', [], 'Modules.Pkelements.Admin'),
                'label_on' => WidgetHelper::getTrans()->trans('Show', [], 'Admin.Actions'),
                'label_off' => WidgetHelper::getTrans()->trans('Hide', [], 'Modules.Pkelements.Admin'),
                'default' => 'yes'
            ]
        );

        $this->addControl(
            'block',
            [
                'label'   => WidgetHelper::getTrans()->trans('Links Blocks', [], 'Modules.Pkelements.Admin'),
                'type'    => ControlsManager::SELECT,
                'options' => $this->getBlocks(true),
                'default' => $this->getBlocks(true, true),
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'section_icon_list',
            [
                'label' => WidgetHelper::getTrans()->trans('List', [], 'Modules.Pkelements.Admin'),
                'tab' => ControlsManager::TAB_STYLE,
            ]
        );

        $this->addResponsiveControl(
            'space_between',
            [
                'label' => WidgetHelper::getTrans()->trans('Space Between', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::SLIDER,
                'range' => [
                    'px' => [
                        'max' => 50,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child)' => 'padding-bottom: calc({{SIZE}}{{UNIT}}/2)',
                    '{{WRAPPER}} .widget-pklinks li:not(:first-child)' => 'margin-top: calc({{SIZE}}{{UNIT}}/2)',
                ],
            ]
        );

        $this->addControl(
            'list_style',
            [
                'label'    => WidgetHelper::getTrans()->trans('List Style', [], 'Modules.Pkelements.Admin'),
                'type'     => ControlsManager::SELECT,
                'label_block' => true,
                'multiple' => false,
                'options'  => [
                    'none' => WidgetHelper::getTrans()->trans('None', [], 'Admin.Global'),
                    'disc' => WidgetHelper::getTrans()->trans('Disc', [], 'Modules.Pkelements.Admin'),
                    'circle' => WidgetHelper::getTrans()->trans('Circle', [], 'Modules.Pkelements.Admin'),
                    'square' => WidgetHelper::getTrans()->trans('Square', [], 'Modules.Pkelements.Admin'),
                ],
                'default'   => 'none',
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li' => 'list-style:{{VALUE}};list-style-position:inside;',
                ],
            ]
        );

        $this->addResponsiveControl(
            'icon_align',
            [
                'label' => WidgetHelper::getTrans()->trans('Alignment', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => WidgetHelper::getTrans()->trans('Left', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-left',
                    ],
                    'center' => [
                        'title' => WidgetHelper::getTrans()->trans('Center', [], 'Modules.Pkelements.Admin'),
                        'icon' => 'fa fa-align-center',
                    ],
                    'right' => [
                        'title' => WidgetHelper::getTrans()->trans('Right', [], 'Admin.Global'),
                        'icon' => 'fa fa-align-right',
                    ],
                ],
                'prefix_class' => 'elementor%s-align-',
            ]
        );

        $this->addControl(
            'divider',
            [
                'label' => WidgetHelper::getTrans()->trans('Divider', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::SWITCHER,
                'label_off' => WidgetHelper::getTrans()->trans('Off', [], 'Modules.Pkelements.Admin'),
                'label_on' => WidgetHelper::getTrans()->trans('On', [], 'Modules.Pkelements.Admin'),
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'content: "";display: block;',
                ],
                'separator' => 'after',
            ]
        );

        $this->addControl(
            'divider_style',
            [
                'label' => WidgetHelper::getTrans()->trans('Style', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::SELECT,
                'options' => [
                    'solid' => WidgetHelper::getTrans()->trans('Solid', [], 'Modules.Pkelements.Admin'),
                    'double' => WidgetHelper::getTrans()->trans('Double', [], 'Modules.Pkelements.Admin'),
                    'dotted' => WidgetHelper::getTrans()->trans('Dotted', [], 'Modules.Pkelements.Admin'),
                    'dashed' => WidgetHelper::getTrans()->trans('Dashed', [], 'Modules.Pkelements.Admin'),
                ],
                'default' => 'solid',
                'condition' => [
                    'divider' => 'yes',
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'border-top-style: {{VALUE}}',
                ],
            ]
        );

        $this->addControl(
            'divider_weight',
            [
                'label' => WidgetHelper::getTrans()->trans('Weight', [], 'Admin.Global'),
                'type' => ControlsManager::SLIDER,
                'default' => [
                    'size' => 1,
                ],
                'range' => [
                    'px' => [
                        'min' => 1,
                        'max' => 20,
                    ],
                ],
                'condition' => [
                    'divider' => 'yes',
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'border-top-width: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->addControl(
            'divider_width',
            [
                'label' => WidgetHelper::getTrans()->trans('Width', [], 'Admin.Global'),
                'type' => ControlsManager::SLIDER,
                'default' => [
                    'unit' => '%',
                ],
                'condition' => [
                    'divider' => 'yes',
                    'view!' => 'inline',
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'width: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->addControl(
            'divider_height',
            [
                'label' => WidgetHelper::getTrans()->trans('Height', [], 'Admin.Global'),
                'type' => ControlsManager::SLIDER,
                'size_units' => [ '%', 'px' ],
                'default' => [
                    'unit' => '%',
                ],
                'range' => [
                    'px' => [
                        'min' => 1,
                        'max' => 100,
                    ],
                    '%' => [
                        'min' => 1,
                        'max' => 100,
                    ],
                ],
                'condition' => [
                    'divider' => 'yes',
                    'view' => 'inline',
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'height: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->addControl(
            'divider_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Color', [], 'Admin.Catalog.Feature'),
                'type' => ControlsManager::COLOR,
                'default' => '#ddd',
                'scheme' => [
                    'type' => SchemeColor::getType(),
                    'value' => SchemeColor::COLOR_3,
                ],
                'condition' => [
                    'divider' => 'yes',
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:not(:last-child):after' => 'border-color: {{VALUE}}',
                ],
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'section_text_style',
            [
                'label' => WidgetHelper::getTrans()->trans('Links', [], 'Modules.Pkelements.Admin'),
                'tab' => ControlsManager::TAB_STYLE,
            ]
        );

        $this->addControl(
            'text_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Link Color', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li' => 'color: {{VALUE}};',
                ],
                'scheme' => [
                    'type' => SchemeColor::getType(),
                    'value' => SchemeColor::COLOR_2,
                ],
            ]
        );

        $this->addControl(
            'text_color_hover',
            [
                'label' => WidgetHelper::getTrans()->trans('Hover', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->addControl(
            'text_indent',
            [
                'label' => WidgetHelper::getTrans()->trans('Link Indent', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::SLIDER,
                'range' => [
                    'px' => [
                        'max' => 50,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks li' => is_rtl() ? 'padding-right: {{SIZE}}{{UNIT}};' : 'padding-left: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->addGroupControl(
            GroupControlTypography::getType(),
            [
                'name' => 'icon_typography',
                'selector' => '{{WRAPPER}} .widget-pklinks li',
                'scheme' => SchemeTypography::TYPOGRAPHY_3,
            ]
        );

        $this->endControlsSection();

        $this->startControlsSection(
            'title_style',
            [
                'label' => WidgetHelper::getTrans()->trans('Title', [], 'Admin.Global'),
                'tab' => ControlsManager::TAB_STYLE,
            ]
        );

        $this->addControl(
            'title_color',
            [
                'label' => WidgetHelper::getTrans()->trans('Title Color', [], 'Modules.Pkelements.Admin'),
                'type' => ControlsManager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .widget-pklinks-title' => 'color: {{VALUE}};',
                    '#footer {{WRAPPER}} .widget-pklinks-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->addGroupControl(
            GroupControlTypography::getType(),
            [
                'name' => 'title_typography',
                'selector' => '{{WRAPPER}} .widget-pklinks-title',
                'scheme' => SchemeTypography::TYPOGRAPHY_3,
            ]
        );

        $this->addControl(
            'title_margin',
            [
                'label'      => WidgetHelper::getTrans()->trans('Title Margin', [], 'Modules.Pkelements.Admin'),
                'type'       => ControlsManager::DIMENSIONS,
                'selectors'  => [
                    '{{WRAPPER}} .widget-pklinks-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->endControlsSection();
    }

    protected function render()
    {
        if (is_admin())
        {
            return print '<div class="ce-remote-render"></div>';
        }

        if (!Module::isInstalled($this->module_name))
        {
            echo '<div class="elementor-alert elementor-alert-danger">"Link List" module is not installed</div>';
            return;
        }

        $widgetTemplate = new WidgetTemplate;
        $settings = $this->getSettings();

        $cache = WidgetCache::getInstance();
        $cache_id = WidgetCache::getCacheId($settings);
        $cached_data = $cache->getVar($cache_id);

        if ($cached_data === false || Tools::getValue('render'))
        {
            $settings['links'] = $this->getBlock($settings['block']);
            Context::getContext()->smarty->assign($settings);
            
            $settings['title'] = $widgetTemplate->getHeading($settings['title']);
            $settings['content'] = $widgetTemplate->getTemplate($this->getName());
            $settings['params'] = WidgetHelper::getCarouselOptions($settings);

            $cache->setVar($cache_id, $settings);
        } else {
            $settings = $cached_data;
        }

        Context::getContext()->smarty->assign($settings);

        $widgetTemplate->echoWidget();
    }

    public function getBlock($block_id)
    {
        foreach ($this->getBlocks(false, false, $block_id) as $block)
        {
            if ($block['id'] == $block_id)
            {
                return $block;
            }
        }
        return [];
    }

    public function getBlocks($for_admin = false, $get_first = false, $id = null)
    {
        $cache = WidgetCache::getInstance();
        $cache_id = WidgetCache::getCacheId(['WidgetPklinks::getBlocks', $id]);
        $admin_blocks = $cache->getVar($cache_id);

        if ($admin_blocks === false || Tools::getValue('render') || true)
        {
            $blocks = [];
            
            $ps_links = Module::getInstanceByName($this->module_name);
            $id_hooks = Db::getInstance()->executeS('SELECT `id_hook` FROM '._DB_PREFIX_.'link_block');

            if (!empty($id_hooks))
            {
                foreach ($id_hooks as $hook)
                {
                    if (!is_string($hookName = Hook::getNameById($hook['id_hook']))) {
                        $hookName = Db::getInstance()->getValue('SELECT `name` FROM `' . _DB_PREFIX_ . 'hook` WHERE `id_hook` = ' . (int) $hook['id_hook']);

                        if (!is_string($hookName)) {
                            return $blocks;
                        }
                    }

                    $block = $ps_links->getWidgetVariables($hookName, []);
                    $blocks = array_merge($blocks, $block['linkBlocks']);
                }
            }

            /*
            $hookName = 'displayFooter';
            $block = $ps_links->getWidgetVariables($hookName, []);
            $id_hook = Hook::getIdByName($hookName);

            $linkBlockPresenter = new LinkBlockPresenter(new \Link(), \Context::getContext()->language);
            $legacyBlockRepository = new LegacyLinkBlockRepository(\Db::getInstance(), \Context::getContext()->shop, \Context::getContext()->getTranslator());

            $linkBlocks = $legacyBlockRepository->getByIdHook($id_hook);

            foreach ($linkBlocks as $block) {
                $blocks[] = $linkBlockPresenter->present($block);
            }
*/
            $blocks = array_merge($blocks, $block['linkBlocks']);

            if (!$for_admin || empty($blocks))
            {
                return $blocks;
            }

            if ($get_first)
            {
                return $blocks[0]['id'];
            }

            $admin_blocks = [];
            foreach ($blocks as $block)
            {
                $admin_blocks[$block['id']] = $block['title'];
            }

            // $cache->setVar($cache_id, $admin_blocks);
        }

        return $admin_blocks;
    }
}