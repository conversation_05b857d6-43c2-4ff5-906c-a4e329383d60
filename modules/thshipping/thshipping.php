<?php

/**
 * Shipping management
 *
 * <AUTHOR>
 * @copyright THECON
 * @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

$autoloadPath = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoloadPath)) {
    require_once $autoloadPath;
}

// Include integration classes
require_once __DIR__ . '/classes/QuantityDiscountIntegration.php';

use PrestaShop\PrestaShop\Adapter\SymfonyContainer;
use ThShipping\Config\ShippingTypes;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use PrestaShopBundle\Form\Admin\Type\CustomContentType;
use PrestaShop\PrestaShop\Adapter\Presenter\Cart\CartPresenter;
use PrestaShop\PrestaShop\Adapter\Image\ImageRetriever;
use PrestaShop\PrestaShop\Adapter\Product\PriceFormatter;
use PrestaShop\PrestaShop\Core\Product\ProductListingPresenter;
use PrestaShop\PrestaShop\Adapter\Product\ProductColorsRetriever;

class ThShipping extends CarrierModule
{
    public static $kernel = null;
    private $id_tax_rules_group = 9;
    private $presenter;
    private $assembler;
    private $presentationSettings;

    public function __construct()
    {
        $this->name = 'thshipping';
        $this->tab = 'shipping_logistics';
        $this->version = '1.0.0';
        $this->author = 'Thecon';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '8.0.0',
            'max' => _PS_VERSION_,
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Shipping management and split order');
        $this->description = $this->l('Adds a custom shipping type (box/envelope) to products and manage shipping costs.');

        $this->confirmUninstall = $this->l('Are you sure you want to uninstall? This will remove the custom shipping data.');

        // Load product presenter
        $this->assembler = new ProductAssembler($this->context);
        $this->presentationSettings = (new ProductPresenterFactory($this->context))->getPresentationSettings();
        $this->presenter = new ProductListingPresenter(
            new ImageRetriever(
                $this->context->link
            ),
            $this->context->link,
            new PriceFormatter(),
            new ProductColorsRetriever(),
            $this->context->getTranslator()
        );
    }

    /**
     * Module installation process.
     * @return bool Success or failure
     */
    public function install()
    {
        include_once(dirname(__FILE__) . '/sql/install.php');

        return
            parent::install() &&
            $this->registerHook('actionAdminControllerSetMedia') &&
            $this->registerHook('displayCarrierZonesFooter') &&
            $this->registerHook('actionSupplierFormBuilderModifier') &&
            $this->registerHook('actionAfterCreateSupplierFormHandler') &&
            $this->registerHook('actionAfterUpdateSupplierFormHandler') &&
            $this->registerHook('actionObjectSupplierDeleteAfter') &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('actionFrontControllerSetVariables') &&
            $this->registerHook('actionFrontControllerInitBefore') &&
            $this->registerHook('actionFrontControllerSetMedia') &&
            $this->registerHook('actionFilterDeliveryOptionList') &&
            $this->registerHook('actionValidateOrder') &&
            $this->registerHook('actionValidateStepComplete') &&
            $this->registerHook('actionObjectCartUpdateAfter') &&
            $this->registerHook('displayOrderConfirmation') &&
            $this->registerHook('displayCarrierExtraContent') &&
            $this->registerHook('actionCartSave') &&
            $this->registerHook('actionCartRuleAfterAdd') &&
            $this->registerHook('actionCartRuleAfterRemove') &&
            $this->registerHook('actionCarrierProcess') &&
            $this->addTab((int)Tab::getIdFromClassName('AdminParentShipping'), 'AdminThShippingProductTypes', $this->l('Product Shipping Types'), '', 'thshipping_admin_product_shipping_index');
    }

    /**
     * Module uninstallation process.
     * @return bool Success or failure
     */
    public function uninstall()
    {
        include_once(dirname(__FILE__) . '/sql/uninstall.php');

        foreach (array_keys($this->getConfigFormValues()) as $key) {
            Configuration::deleteByName($key);
        }

        return
            parent::uninstall() &&
            $this->removeTab('AdminThShippingProductTypes') &&
            $this->removeTab('AdminThShippingCarriers');
    }

    /**
     * Add a new tab to the back office menu.
     *
     * @param int $id_parent The ID of the parent tab.
     * @param string $class_name The class name of the controller for the tab.
     * @param string $name The name of the tab.
     * @param string $icon The icon for the tab.
     * @param string $route_name The route name for the tab.
     * @return bool True on success, false on failure.
     */
    public function addTab($id_parent, $class_name, $name, $icon = '', $route_name = ''): bool
    {
        $tab = new Tab();
        $tab->id_parent = $id_parent;
        $tab->class_name = $class_name;
        $tab->icon = $icon;
        $tab->module = $this->name;
        $tab->route_name = $route_name;
        $tab->active = 1;

        $tab->name = array();
        foreach (Language::getLanguages() as $language) {
            $tab->name[$language['id_lang']] = $name;
        }

        return $tab->add();
    }

    /**
     * Removes a tab from the back office menu.
     *
     * @param string $class_name The class name of the controller for the tab to remove.
     * @return bool True on success or if the tab does not exist, false on failure.
     */
    private function removeTab($class_name): bool
    {
        if ($id_tab = Tab::getIdFromClassName($class_name)) {
            $tab = new Tab($id_tab);
            if (Validate::isLoadedObject($tab)) {
                return $tab->delete();
            }

            return false;
        }

        return true;
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        $message = '';

        if (Tools::isSubmit('submitAddCarrier')) {
            $this->addCarrier();

            $message .= $this->displayConfirmation($this->l('Carrier added successfully!'));
        }

        if (Tools::isSubmit('submit' . ucfirst($this->name) . 'Module') && !Tools::isSubmit('submitAddCarrier')) {
            $this->postProcess();

            //$this->uninstallOverrides();
            //$this->installOverrides();

            //$this->registerHook('actionObjectCartUpdateAfter');

            $message = count($this->_errors) ? $this->displayError($this->_errors) : $this->displayConfirmation($this->l('Successfully saved!'));
        }

        return $message . $this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submit' . ucfirst($this->name) . 'Module';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = array(
            'fields_value' => $this->getConfigFormValues(), /* Add values for your inputs */
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        return $helper->generateForm(array($this->getConfigForm()));
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm(): array
    {
        $data = array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Settings'),
                    'icon' => 'icon-cogs',
                ),
                'input' => array(
                    array(
                        'type' => 'html_title',
                        'name' => $this->l('Default configuration'),
                    ),
                    array(
                        'type' => 'text',
                        'name' => 'THSHIPPING_DEFAULT_WEIGHT',
                        'label' => $this->l('Default weight'),
                        'required' => true,
                        'col' => 2,
                    ),
                    array(
                        'type' => 'select',
                        'label' => $this->l('No match weight behavior'),
                        'name' => 'THSHIPPING_NO_MATCH_WEIGHT',
                        'class' => 'fixed-width-xxl',
                        'options' => array(
                            'query' => [
                                [
                                    'id' => 0,
                                    'name' => $this->l('Lowest rule'),
                                ],
                                [
                                    'id' => 1,
                                    'name' => $this->l('Highest rule'),
                                ],
                            ],
                            'id' => 'id',
                            'name' => 'name'
                        ),
                    ),
                    array(
                        'type' => 'select',
                        'label' => $this->l('Default shipping type'),
                        'name' => 'THSHIPPING_DEFAULT_PRODUCT_TYPE',
                        'class' => 'fixed-width-xxl',
                        'options' => array(
                            'query' => array_map(function ($type) {
                                return [
                                    'id' => $type,
                                    'name' => $type,
                                ];
                            }, ShippingTypes::getFormChoices()),
                            'id' => 'id',
                            'name' => 'name'
                        ),
                    ),
                    array(
                        'type' => 'html_title',
                        'name' => $this->l('Carrier data'),
                    ),
                    array(
                        'type' => 'text',
                        'name' => 'THSHIPPING_CARRIER_NAME',
                        'label' => $this->l('Carrier name'),
                        'required' => true,
                        'col' => 5,
                    ),
                    array(
                        'type' => 'text',
                        'name' => 'THSHIPPING_CARRIER_DELIVERY_TIME',
                        'label' => $this->l('Delivery time'),
                        'required' => true,
                        'col' => 5,
                    ),
                    array(
                        'type' => 'text',
                        'name' => 'THSHIPPING_CARRIER_EXTRA_CONTENT',
                        'label' => $this->l('Extra content'),
                        'required' => true,
                        'col' => 5,
                    ),
                    array(
                        'type' => 'html_title',
                        'name' => $this->l('Feature shipping type configuration'),
                    ),
                    array(
                        'type' => 'text',
                        'name' => 'THSHIPPING_FEATURE_SHIPPING_TYPE',
                        'label' => $this->l('Feature shipping type'),
                        'required' => true,
                        'col' => 2,
                    ),
                ),
                'buttons' => array(
                    array(
                        'title' => $this->l('Add Carrier'),
                        'name' => 'submitAddCarrier',
                        'type' => 'submit',
                    ),
                    array(
                        'title' => $this->l('Save'),
                        'name' => 'submit' . ucfirst($this->name) . 'Module',
                        'type' => 'submit',
                        'class' => 'pull-right',
                    ),
                ),
            ),
        );

        foreach (ShippingTypes::getFormChoices() as $shippingType) {
            $data['form']['input'][] = array(
                'type' => 'text',
                'name' => 'THSHIPPING_FEATURE_SHIPPING_TYPE_' . $shippingType,
                'label' => $this->l('Feature value shipping type for "' . $shippingType . '"'),
                'required' => true,
                'col' => 2,
            );
        }


        return $data;
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues(): array
    {
        $data = array(
            'THSHIPPING_DEFAULT_WEIGHT' => Tools::getValue('THSHIPPING_DEFAULT_WEIGHT', Configuration::get('THSHIPPING_DEFAULT_WEIGHT')),
            'THSHIPPING_NO_MATCH_WEIGHT' => Tools::getValue('THSHIPPING_NO_MATCH_WEIGHT', Configuration::get('THSHIPPING_NO_MATCH_WEIGHT')),
            'THSHIPPING_DEFAULT_PRODUCT_TYPE' => Tools::getValue('THSHIPPING_DEFAULT_PRODUCT_TYPE', Configuration::get('THSHIPPING_DEFAULT_PRODUCT_TYPE')),
            'THSHIPPING_FEATURE_SHIPPING_TYPE' => Tools::getValue('THSHIPPING_FEATURE_SHIPPING_TYPE', Configuration::get('THSHIPPING_FEATURE_SHIPPING_TYPE')),
            'THSHIPPING_CARRIER_NAME' => Tools::getValue('THSHIPPING_CARRIER_NAME', Configuration::get('THSHIPPING_CARRIER_NAME')),
            'THSHIPPING_CARRIER_DELIVERY_TIME' => Tools::getValue('THSHIPPING_CARRIER_DELIVERY_TIME', Configuration::get('THSHIPPING_CARRIER_DELIVERY_TIME')),
            'THSHIPPING_CARRIER_EXTRA_CONTENT' => Tools::getValue('THSHIPPING_CARRIER_EXTRA_CONTENT', Configuration::get('THSHIPPING_CARRIER_EXTRA_CONTENT')),
        );

        foreach (ShippingTypes::getFormChoices() as $shippingType) {
            $data['THSHIPPING_FEATURE_SHIPPING_TYPE_' . $shippingType] = Tools::getValue('THSHIPPING_FEATURE_SHIPPING_TYPE_' . $shippingType, Configuration::get('THSHIPPING_FEATURE_SHIPPING_TYPE_' . $shippingType));
        }

        return $data;
    }

    /**
     * Save form data.
     */
    protected function postProcess(): void
    {
        foreach (array_keys($this->getConfigFormValues()) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
    }

    /**
     * Retrieves the application kernel instance using the Singleton pattern.
     *
     * @return AppKernel|null The application kernel instance or null if not available.
     */
    public static function getKernel(): ?AppKernel
    {
        if (!self::$kernel) {
            global $kernel;
            if ($kernel) {
                self::$kernel = $kernel;
            } else {
                require_once _PS_ROOT_DIR_ . '/app/AppKernel.php';

                $env = _PS_MODE_DEV_ ? 'dev' : 'prod';
                $debug = _PS_MODE_DEV_;

                self::$kernel = new AppKernel($env, $debug);
                self::$kernel->boot();
            }
        }

        return self::$kernel;
    }

    public function hookActionAdminControllerSetMedia(array $params): void
    {
        if (Tools::getValue('configure') === $this->name) {
            $this->context->controller->addCSS($this->getLocalPath() . 'views/css/configure.css');
        }
    }

    // called from /[admin_dir]/themes/default/template/controllers/carrier_wizard/helpers/form.tpl
    public function hookDisplayCarrierZonesFooter(): string
    {
        $id_reference = (new Carrier((int)Tools::getValue('id_carrier')))->id_reference;
        if (!$id_reference) {
            return '';
        }

        $this->context->smarty->assign([
            'configure_url' => SymfonyContainer::getInstance()->get('router')->generate('th_shipping_config_index', ['id_reference' => $id_reference]),
        ]);

        return $this->display(__FILE__, 'views/templates/admin/button.tpl', $this->getCacheId('th_shipping|displayCarrierZonesFooter'));
    }

    /**
     * Hook to modify the Supplier form.
     *
     * @param array $params
     */
    public function hookActionSupplierFormBuilderModifier(array $params): void
    {
        /** @var \Symfony\Component\Form\FormBuilderInterface $formBuilder */
        $formBuilder = $params['form_builder'];

        // Add a custom HTML separator with a badge
        $formBuilder->add('th_custom_separator', CustomContentType::class, [
            'template' => '@Modules/thshipping/views/templates/admin/_partials/form_separator.html.twig',
            'data' => [
                'th_separator_title' => $this->displayName,
            ],
            'label' => false, // No label for the separator itself
            'mapped' => false, // This field is not mapped to any entity property
            'required' => false,
        ]);

        // Load supplier data
        $supplierDataService = $this->get('ThShipping\Service\SupplierDataService');
        $supplierData = $supplierDataService->getSupplierData($params['id'] ?? 0);

        // Add a Choice field to select Carrier
        $activeCarriers = Carrier::getCarriers(
            $this->context->language->id,
            true,
            false,
            null,
            null,
            Carrier::ALL_CARRIERS
        );
        $carrierChoices = array_column(array_filter($activeCarriers, function ($carrier) {
            return isset($carrier['id_reference']) && $carrier['id_reference'] !== null && (int)$carrier['id_reference'] > 0;
        }), 'id_reference', 'name');
        $formBuilder->add('th_custom_supplier_field', ChoiceType::class, [
            'label' => $this->l('Carrier'),
            'required' => false,
            'choices' => $carrierChoices,
            'placeholder' => $this->l('Select a carrier'),
            'data' => $supplierData ? $supplierData->getIdReference() : null,
            'attr' => [
                'data-toggle' => 'select2',
                'data-minimumResultsForSearch' => '7',
            ],
        ]);


        // Add a Choice field to select Invoicer
        $invoicers = [];
        if (Module::isEnabled('ets_marketplace')) {
            $sql = new DbQuery();
            $sql->select('s.id_seller, sl.shop_name')
                ->from('ets_mp_seller', 's')
                ->leftJoin('ets_mp_seller_lang', 'sl', 's.id_seller = sl.id_seller AND sl.id_lang = ' . $this->context->language->id)
                ->where('s.active = 1');
            $invoicers = Db::getInstance()->executeS($sql);

            if ($invoicers) {
                $invoicers = array_column($invoicers, 'id_seller', 'shop_name');
            }
        }

        $formBuilder->add('th_invoicer_field', ChoiceType::class, [
            'label' => $this->l('Invoicer'),
            'required' => false,
            'choices' => $invoicers,
            'placeholder' => $this->l('Select an invoicer'),
            'data' => $supplierData ? $supplierData->getIdInvoicer() : null,
            'attr' => [
                'data-toggle' => 'select2',
                'data-minimumResultsForSearch' => '7',
            ],
        ]);

        // Add a textarea field for notes
        $formBuilder->add('th_notes_field', TextareaType::class, [
            'label' => $this->l('Notes'),
            'required' => false,
            'data' => $supplierData ? $supplierData->getNotes() : null,
            'attr' => [
                'rows' => 8,
                'placeholder' => $this->l('Enter any additional notes for this supplier... HTML tags are allowed.'),
                'class' => 'form-control autoload_rte', // This will enable rich text editor if available
            ],
        ]);
    }

    /**
     * Hook called after a new supplier is added.
     *
     * @param array $params
     */
    public function hookActionAfterCreateSupplierFormHandler(array $params): void
    {
        $this->processSupplierCustomData($params);
    }

    /**
     * Hook called after an existing supplier is updated.
     *
     * @param array $params
     */
    public function hookActionAfterUpdateSupplierFormHandler(array $params): void
    {
        $this->processSupplierCustomData($params);
    }

    /**
     * Hook called after a supplier is deleted.
     *
     * @param array $params
     */
    public function hookActionObjectSupplierDeleteAfter(array $params): void
    {
        try {
            $supplierDataService = $this->get('ThShipping\Service\SupplierDataService');
            $supplierDataService->deleteSupplierData((int)$params['object']->id);
        } catch (\Exception $e) {
            PrestaShopLogger::addLog(
                'ThShipping module: Failed to delete custom supplier data for supplier ID ' . $params['object']->id . '. Error: ' . $e->getMessage(),
                3
            );
        }
    }

    /**
     * Processes and saves custom supplier data from form submission.
     *
     * @param array $params Hook parameters containing 'id' (supplier ID) and 'form_data' (array of submitted form data).
     */
    private function processSupplierCustomData(array $params): void
    {
        $supplierId = $params['id'];
        $idReference = $params['form_data']['th_custom_supplier_field'] ?? 0;
        $idInvoicer = $params['form_data']['th_invoicer_field'] ?? 0;
        $notes = $params['form_data']['th_notes_field'] ?? '';

        try {
            $supplierDataService = $this->get('ThShipping\Service\SupplierDataService');
            $supplierDataService->updateSupplierData((int)$supplierId, (int)$idReference, (int)$idInvoicer, $notes);
        } catch (\Exception $e) {
            PrestaShopLogger::addLog(
                'ThShipping module: Failed to save custom supplier data for supplier ID ' . $supplierId . '. Error: ' . $e->getMessage(),
                3
            );
        }
    }

    /**
     * Hook to display additional product information.
     *
     * @param array $params Hook parameters containing product information.
     * @return string HTML content to be displayed.
     */
    public function hookDisplayProductAdditionalInfo(array $params): string
    {
        $sql = new DbQuery();
        $sql->select('id_invoicer');
        $sql->from('th_supplier_data');
        $sql->where('id_supplier = ' . (int)$params['product']->id_supplier);
        $id_invoicer = Db::getInstance()->getValue($sql);

        if (!$id_invoicer) {
            return '';
        }

        $sql = new DbQuery();
        $sql->select('CONCAT(emsl.`shop_name`, " - ", emsl.`shop_address`, " - ' . $this->l('UST-IDNR') . ': ", ems.`vat_number`)');
        $sql->from('ets_mp_seller_lang', 'emsl');
        $sql->innerJoin('ets_mp_seller', 'ems', 'ems.id_seller = emsl.id_seller');
        $sql->where('ems.id_seller = ' . (int)$id_invoicer);
        $sql->where('emsl.id_lang = ' . (int)$this->context->language->id);
        $invoicer = Db::getInstance()->getValue($sql);

        if (!$invoicer) {
            return '';
        }

        $this->context->smarty->assign([
            'th_invoicer' => $invoicer,
        ]);

        return $this->display(__FILE__, 'views/templates/hook/product_additional_info.tpl', $this->getCacheId('th_shipping|product_additional_info|' . (int)$params['product']->id_supplier));
    }

    /**
     * Hook to set variables for the cart page.
     *
     * @param array $params Hook parameters containing template variables.
     */
    public function hookActionFrontControllerSetVariables($params): void
    {
        $action = Tools::getValue('action');
        if ($this->context->controller->php_self === 'cart' && (!$action || $action == 'show')) {
            // Apply discount rules to child carts when cart page is loaded
            if (QuantityDiscountIntegration::isQuantityDiscountProActive() &&
                QuantityDiscountIntegration::hasChildCarts($this->context->cart->id)) {
                QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($this->context->cart->id);
            }

            $cart_data = $this->getThCartData($this->context->cart);
            $params['templateVars']['th_cart'] = $cart_data;
            $params['templateVars']['th_cart_count'] = count(array_filter(array_keys($cart_data), function ($item) {
                return $item !== -1;
            }));
        }
    }

    /**
     * Hook where we check and set cart id to cookie.
     */
    public function hookActionFrontControllerInitBefore()
    {

        // If we are on cart page and we are deleting a discount code, we need to do nothing
        if (Tools::getValue('controller') == 'cart' && (Tools::getIsset('deleteDiscount') || Tools::getIsset('addDiscount'))) {
            return;
        }

        // If we are on this module, we need to do nothing
        if (Tools::getValue('module') == $this->name && Tools::getValue('controller') == 'ajax') {
            return;
        }

        //if we are on a CMS page and its an AJAX request, we need to do nothing
        if (Tools::getValue('controller') == 'cms' && Tools::getValue('id_cms') && Tools::getValue('content_only')) {
            return;
        }

        // On order page we need to set child cart id to cookie
        $payment_modules = array_column(PaymentModule::getInstalledPaymentModules(), 'name');
        if (Tools::getValue('module') && in_array(Tools::getValue('module'), $payment_modules, true)) {
            return;
        }

        $cart_url = $this->context->link->getPageLink('cart');

        if ($this->context->controller->php_self === 'order') {
            // If we have key in url, we need to check if it is valid
            if ($key = Tools::getValue('key')) {
                $cart_data = $this->getCartByKey($key);
                if (!$cart_data || in_array($this->context->cookie->id_cart, [(int)$cart_data['id_cart_child'], (int)$cart_data['id_cart_context']], true)) {
                    Tools::redirect($cart_url);
                }

                // If we have valid key, we need to set cart id to child cart
                $this->context->cookie->id_cart = (int)$cart_data['id_cart_child'];
                $this->context->cookie->th_id_cart = (int)$cart_data['id_cart_context'];

                if (!$this->getCartByChildCart($this->context->cookie->id_cart)) {
                    Tools::redirect($cart_url);
                }
            } else {
                // If we don't have key in url, we need to check if current cart is child cart
                $cart_data = $this->getCartByChildCart($this->context->cookie->id_cart);
                if (!$cart_data || (int)$cart_data['id_cart_context'] !== (int)$this->context->cookie->th_id_cart) {
                    Tools::redirect($cart_url);
                }
            }
        } else {
            // On another pages we need to have always parent cart id in cookie
            if ($this->context->cookie->th_id_cart) {
                // If we have parent cart id in cookie, simply set it to cookie
                $this->context->cookie->id_cart = (int)$this->context->cookie->th_id_cart;

                // Reset th_id_cart to 0
                $this->context->cookie->th_id_cart = 0;
            } else {
                // If we don't have parent cart id in cookie, we need to load it from current cart
                $cart_data = $this->getCartByChildCart($this->context->cookie->id_cart);
                if ($cart_data) {
                    $this->context->cookie->id_cart = (int)$cart_data['id_cart_context'];
                } else {
                    // do nothing because current cart is not child cart
                }
            }
        }

        // Apply discount rules to child carts when any page is loaded
        if (QuantityDiscountIntegration::isQuantityDiscountProActive() &&
            QuantityDiscountIntegration::hasChildCarts($this->context->cart->id)) {
            QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($this->context->cart->id);
        }
    }

    /**
     * Set cart id to cookie.
     *
     * @return array Array with success and message.
     */
    public function setCartId()
    {
        $id_cart = $this->encodeNumber(Tools::getValue('id_cart'), 'decode', 'xor');
        $parent_cart = $this->getCartByChildCart((int)$id_cart);

        if (!$parent_cart || (int)$this->context->cart->id !== (int)$parent_cart['id_cart_context']) {
            return ['success' => false, 'message' => 'Cart not found!'];
        }

        $this->context->cookie->id_cart = (int)$parent_cart['id_cart_child'];
        $this->context->cookie->th_id_cart = (int)$parent_cart['id_cart_context'];

        // Update shipping_extracontent for all child carts
        $childCarts = $this->getChildCarts((int)$parent_cart['id_cart_context']);
        foreach ($childCarts as $invoicer_id => $childCartData) {
            $cart = new Cart((int)$childCartData['id_cart_child']);
            $invoicer_data = [
                'id_cart' => (int)$childCartData['id_cart_child'],
                'suppliers' => [], // If needed, populate with actual suppliers
            ];
            $this->addShippingCostsToSupplier($invoicer_data);
        }

        return ['success' => true, 'message' => 'Cart refreshed!'];
    }

    /**
     * Hook to set media.
     */
    public function hookActionFrontControllerSetMedia()
    {
        if ($this->context->controller->php_self === 'order') {
            Media::addJsDef([
                'TH_SHIPPING_AJAX_URL' => $this->context->link->getModuleLink($this->name, 'ajax', ['token' => Tools::getToken(false)]),
                'TH_CURRENT_CART_ID' => $this->encodeNumber($this->context->cart->id, 'encode', 'xor'),
                'TH_CURRENT_CART_KEY' => $this->encodeNumber($this->context->cart->id, 'encode', 'xor'),
            ]);

            $this->context->controller->addJS($this->getLocalPath() . 'views/js/checkout.js');
            $this->context->controller->addJS($this->getLocalPath() . 'views/js/discount-sync.js');
        }

        if ($this->context->controller->php_self === 'cart') {
            Media::addJsDef([
                'TH_SHIPPING_AJAX_URL' => $this->context->link->getModuleLink($this->name, 'ajax', ['token' => Tools::getToken(false)]),
            ]);

            $this->context->controller->addJS($this->getLocalPath() . 'views/js/cart.js');
            $this->context->controller->addJS($this->getLocalPath() . 'views/js/discount-sync.js');
        }
    }

    /**
     * Hook to filter delivery option list.
     *
     * @param array $params Hook parameters containing delivery option list.
     */
    public function hookActionFilterDeliveryOptionList($params)
    {
        foreach ($params['delivery_option_list'] as &$address) {
            foreach ($address as $id_carrier => &$carrier) {
                $id_reference = (new Carrier(str_replace(',', '', $id_carrier)))->id_reference;

                if ((int)$id_reference !== (int)Configuration::get('THSHIPPING_CARRIER_ID')) {
                    unset($address[$id_carrier]);
                } else {
                    $cost = $this->getShippingCost($params['cart']);

                    if (!$cost['valid']) {
                        unset($address[$id_carrier]);
                        continue;
                    }

                    // Update db shipping costs
                    Db::getInstance()->update('th_s_cart', [
                        'shipping_cost_tax_incl' => $cost['total_price_with_tax'],
                        'shipping_cost_tax_excl' => $cost['total_price_without_tax'],
                    ], 'id_cart_child = ' . (int)$params['cart']->id);

                    // Update carrier data
                    $carrier['total_price_with_tax'] = $cost['total_price_with_tax'];
                    $carrier['total_price_without_tax'] = $cost['total_price_without_tax'];
                    $carrier['is_free'] = false;
                }
            }
            unset($carrier);
        }
        unset($address);
    }

    /**
     * Hook to validate order.
     *
     * @param array $params Hook parameters containing order information.
     */
    public function hookActionValidateOrder($params)
    {
        $cart = $params['cart'];
        $order = $params['order'];

        // Check if cart is child cart
        $cart_data = $this->getCartByChildCart($cart->id);
        if (!$cart_data) {
            return;
        }

        // Update cart paid status
        Db::getInstance()->update('th_s_cart', ['paid' => 1], 'id_cart_child = ' . (int)$cart->id);

        // Update order tracking for discount rules
        if (QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            QuantityDiscountIntegration::updateOrderTrackingForChildCart($order->id, $cart->id);
        }

        // Remove products from parent cart
        $parent_cart = new Cart($cart_data['id_cart_context']);
        foreach ($cart->getProducts() as $product) {
            $parent_cart->deleteProduct($product['id_product'], $product['id_product_attribute'], $product['id_customization']);
        }
    }

    /**
     * Validates that all cart products can be shipped to the chosen address.
     *
     * @param array $params Hook parameters containing the cart object.
     */
    public function hookActionValidateStepComplete($params)
    {
        $result = $this->checkAddress($params['cart']);

        if ($result['success']) {
            return;
        }

        $params['completed'] = false;

        $this->context->smarty->assign([
            'th_products' => $result['products'],
        ]);

        $this->context->controller->errors[] = $this->fetch(_PS_MODULE_DIR_ . $this->name . '/views/templates/hook/validate_step_complete.tpl', $this->getCacheId('th_shipping|validate_step_complete|' . implode('|', array_column($result['products'], 'id'))));
    }

    /**
     * Hook to update parent cart when a child cart is updated.
     *
     * @param array $params
     */
    public function hookActionObjectCartUpdateAfter(array $params)
    {
        if (!isset($params['object']) || !($params['object'] instanceof Cart)) {
            return;
        }

        /** @var Cart $child_cart */
        $child_cart = $params['object'];

        // Check if the updated cart is a child cart
        $cart_data = $this->getCartByChildCart($child_cart->id);
        if (!$cart_data) {
            return; // Not a child cart, do nothing
        }

        // It's a child cart, so we need to update the parent cart
        $id_parent_cart = (int)$cart_data['id_cart_context'];
        $parent_cart = new Cart($id_parent_cart);

        if (!Validate::isLoadedObject($parent_cart)) {
            return; // Parent cart not found
        }

        // Fields to synchronize from child to parent
        $fields_to_sync = [
            'id_shop',
            'id_shop_group',
            'id_lang',
            'id_address_delivery',
            'id_address_invoice',
            'id_currency',
            'id_customer',
            'id_guest',
            'secure_key',
            'recyclable',
            'gift',
            'gift_message',
        ];

        $updated = false;
        foreach ($fields_to_sync as $field) {
            if ($parent_cart->$field != $child_cart->$field) {
                $parent_cart->$field = $child_cart->$field;
                $updated = true;
            }
        }

        if ($updated) {
            $parent_cart->update();
        }
    }

    /**
     * Hook to display order confirmation.
     *
     * @param array $params Hook parameters containing order information.
     * @return string|false The HTML content to display, or false if no content should be displayed.
     */
    public function hookDisplayOrderConfirmation($params)
    {
        $parent_cart = $this->getCartByChildCart($params['order']->id_cart);
        if (!$parent_cart) {
            return false;
        }

        $parent_cart = new Cart($parent_cart['id_cart_context']);
        if (count($parent_cart->getProducts()) === 0) {
            return false;
        }

        return $this->fetch(_PS_MODULE_DIR_ . $this->name . '/views/templates/hook/order_confirmation.tpl', $this->getCacheId('th_shipping|order_confirmation|' . $parent_cart->id));
    }

    /**
     * Get shipping cost for a cart.
     *
     * @param Cart $cart The cart object.
     * @return array The shipping cost data.
     */
    private function getShippingCost(Cart $cart)
    {
        $id_carrier_thshipping = (int)Carrier::getCarrierByReference(Configuration::get('THSHIPPING_CARRIER_ID'))->id;

        // Load cart carrier configuration
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('id_cart_child = ' . (int)$cart->id);
        $shipping_configuration = Db::getInstance()->getRow($sql);

        // If no shipping configuration, remove carrier from list
        if (!$shipping_configuration) {
            return [
                'valid' => false,
            ];
        }

        $shipping_configuration = json_decode($shipping_configuration['shipping'], true);

        $cost = 0;

        foreach ($shipping_configuration as $carrier_id => &$carrier_data) {
            // Get carrier
            $current_carrier = Carrier::getCarrierByReference($carrier_id, $this->context->language->id);

            // Prepare product list
            $product_list = [];
            foreach ($carrier_data['products'] as $key => $product_item) {
                $product_keys = explode('_', $key);

                $product_presented = $this->presenter->present(
                    $this->presentationSettings,
                    $this->assembler->assembleProduct([
                        'id_product' => (int)$product_keys[0],
                        'id_product_attribute' => (int)$product_keys[1],
                    ]),
                    $this->context->language
                )->jsonSerialize();

                $product_presented['quantity'] = $product_item;
                if (!$product_presented['weight']) {
                    $product_presented['weight'] = (float)Configuration::get('THSHIPPING_DEFAULT_WEIGHT');
                }

                $product_list[] = $product_presented;

                // Check if the product has a available carrier. If not, set cost to 0 and break the loop.
                $carrier_list = Carrier::getAvailableCarrierList(
                    new Product($product_presented['id_product']),
                    null,
                    $cart->id_address_delivery,
                    null,
                    $cart
                );
                unset($carrier_list[$id_carrier_thshipping]);
                if (!in_array($current_carrier->id, $carrier_list)) {
                    $cost = 0;
                    break 2;
                    // return [
                    //     'valid' => false,
                    // ];
                }
            }

            // VICTOR - thecon
            // Get envelope
            // vezi daca e box sau envelope cartul:
            // cum: iei produsele din acest cart, cauti un feature, gasesti id feature in config module
            // box si envelope ca valori le gasesti tot acolo (IDS)
            // daca ai min 1 produs in cart cu box, suntem pe box
            // daca ai toate produsele envelope, si nu trec de praguri, esti envelope. daca nu, treci pe box

            $id_zone = Address::getZoneById((int)$cart->id_address_delivery);

            $envelope_cost = $this->getEnvelopeShippingCost($cart, $current_carrier->id, true, null, $product_list, $id_zone);

            // Get box cost
            if ($envelope_cost) {
                $box_cost = $envelope_cost;
            } else {
                $box_cost = $cart->getPackageShippingCost($current_carrier->id, true, null, $product_list);
            }

            // If box cost is false, return false. Carrier is not available.
            if ($box_cost === false) {
                return [
                    'valid' => false,
                ];
            }

            //spamgol
            //BOX COST uses cart -> getPackageShippingCost, care NU imparte cartul in supplieri iar produsele celorlalti supplieri fac sa se teaca pragul de weight
            $carrier_data['carrier_name'] = $current_carrier->name;
            $carrier_data['cost'] = $box_cost;
            $carrier_data['cost_vat_inc'] =  $box_cost * (1 + Tax::getCarrierTaxRate(Carrier::getCarrierByReference(Configuration::get('THSHIPPING_CARRIER_ID'))->id) / 100);

            // Add box cost to total cost
            $cost += $box_cost;
        }
        unset($carrier_data);

        return [
            'data' => $shipping_configuration,
            'valid' => true,
            'total_price_without_tax' => $cost / (1 + Tax::getCarrierTaxRate(Carrier::getCarrierByReference(Configuration::get('THSHIPPING_CARRIER_ID'))->id) / 100),
            'total_price_with_tax' => $cost,
        ];
    }

    private function getEnvelopeShippingCost(
        Cart    $cart,
                $id_carrier = null,
                $use_tax = true,
        Country $default_country = null,
                $product_list = null,
                $id_zone = null
    )
    {
        $configuration_names = array(
            'THSHIPPING_DEFAULT_PRODUCT_TYPE',
            'THSHIPPING_DEFAULT_WEIGHT',
            'THSHIPPING_FEATURE_SHIPPING_TYPE',
            'THSHIPPING_FEATURE_SHIPPING_TYPE_box',
            'THSHIPPING_FEATURE_SHIPPING_TYPE_envelope',
        );

        $configuration = Configuration::getMultiple($configuration_names);

        foreach ($configuration_names as $name) {
            if (!isset($configuration[$name]) || !$configuration[$name]) {
                return false;
            }
        }

        if (null === $id_carrier && !empty($cart->id_carrier)) {
            $id_carrier = (int)$cart->id_carrier;
        }

        if (null === $id_carrier) {
            return false;
        }

        if ($cart->isVirtualCart()) {
            return 0;
        }

        if (!$default_country) {
            $default_country = Context::getContext()->country;
        }

        if (null === $product_list) {
            $products = $cart->getProducts(false, false, null, true);
        } else {
            foreach ($product_list as $key => $value) {
                if ($value['is_virtual'] == 1) {
                    unset($product_list[$key]);
                }
            }
            $products = $product_list;
        }

        if (!$id_zone) {
            $id_zone = Country::getIdZone((int)$default_country->id);
        }


        $carrier = new Carrier($id_carrier);

        if (!$carrier) {
            return false;
        }

        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_carrier');
        $sql->where('id_reference = ' . (int)$carrier->id_reference . ' AND id_zone = ' . (int)$id_zone);
        $row = Db::getInstance()->getRow($sql);

        if (!$row) {
            return false;
        }

        $is_box = false;
        $weight = 0;
        $total_price = 0;
        foreach ($products as $product) {
            $productFeatures = Product::getFeaturesStatic($product['id_product']);

            $shippingTypeFeatureId = (int)$configuration['THSHIPPING_FEATURE_SHIPPING_TYPE'];
            $boxFeatureValueId = (int)$configuration['THSHIPPING_FEATURE_SHIPPING_TYPE_box'];
            $envelopeFeatureValueId = (int)$configuration['THSHIPPING_FEATURE_SHIPPING_TYPE_envelope'];

            $hasShippingTypeFeature = false;

            foreach ($productFeatures as $feature) {
                if ((int)$feature['id_feature'] === $shippingTypeFeatureId) {

                    $hasShippingTypeFeature = true;
                    $featureValueId = (int)$feature['id_feature_value'];
                    if ($featureValueId == $boxFeatureValueId) {

                        $is_box = true;
                        break 2;
                    }
                    break;
                }
            }

            if (!$hasShippingTypeFeature) {
                $defaultProductType = $configuration['THSHIPPING_DEFAULT_PRODUCT_TYPE'];
                if ($defaultProductType == 'box') {
                    $is_box = true;

                    break;
                }
            }

            $weight += floatval($product['weight']) * $product['quantity'];
            $total_price += floatval($product['price_tax_exc']) * $product['quantity'];
        }

        if ($is_box) {
            return false;
        }

        $envelope_max_price = floatval($row['threshold_cost_envelope_box']);
        $envelope_max_weight = $row['threshold_weight_envelope_box'] ? floatval($row['threshold_weight_envelope_box']) : floatval($configuration['THSHIPPING_DEFAULT_WEIGHT']);

        if ($weight > $envelope_max_weight || $total_price > $envelope_max_price) {
            return false;
        }

        if (!isset ($row['cost_envelope']) && !$row['cost_envelope']) {
            return false;
        }
        $cost = (float)$row['cost_envelope'];

        if ($use_tax) {
            $countryId = ($default_country && isset($default_country->id)) ? (int)$default_country->id : (int)Configuration::get('PS_COUNTRY_DEFAULT');
            $idTaxRulesGroup = (int)Carrier::getIdTaxRulesGroupByIdCarrier((int)$carrier->id);

            $sql = new DbQuery();
            $sql->select('t.rate');
            $sql->from('tax', 't');
            $sql->innerJoin('tax_rule', 'tr', 't.id_tax = tr.id_tax');
            $sql->where('tr.id_country = ' . (int)$countryId);
            $sql->where('tr.id_tax_rules_group = ' . (int)$idTaxRulesGroup);
            $sql->where('t.active = 1');
            $sql->where('t.deleted = 0');
            $sql->orderBy('t.rate DESC');

            $taxRate = (float)Db::getInstance()->getValue($sql);

            if ($taxRate <= 0 && (int)$id_zone > 0) {
                $sql = new DbQuery();
                $sql->select('t.rate');
                $sql->from('tax', 't');
                $sql->innerJoin('tax_zone', 'tz', 't.id_tax = tz.id_tax');
                $sql->where('tz.id_zone = ' . (int)$id_zone);
                $sql->where('t.active = 1');
                $sql->where('t.deleted = 0');
                $sql->orderBy('t.rate DESC');
                $taxRate = (float)Db::getInstance()->getValue($sql);
            }

            if ($taxRate > 0) {
                $cost *= (1 + ($taxRate / 100));
            }
        }

        return $cost;
    }

    /**
     * Get shipping cost for a specific child cart and carrier.
     *
     * Called from `Cart->getPackageShippingCost()` override.
     *
     * @param int $id_cart_child The ID of the child cart.
     * @param int $id_carrier The ID of the carrier.
     * @param bool $use_tax Whether to return the cost with tax included.
     * @return float|false The shipping cost, or false if the carrier is not valid or cost is not found.
     */
    public function getShippingCostByCart(int $id_cart_child, int $id_carrier, bool $use_tax = true)
    {
        if ((int)(new Carrier($id_carrier))->id_reference !== (int)Configuration::get('THSHIPPING_CARRIER_ID')) {
            return false;
        }

        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('id_cart_child = ' . (int)$id_cart_child);
        $row = Db::getInstance()->getRow($sql);

        if (!$row) {
            return false;
        }

        return $use_tax ? (float)$row['shipping_cost_tax_incl'] : (float)$row['shipping_cost_tax_excl'];
    }

    /**
     * Get a list of suppliers.
     *
     * @param array $suppliers Array of supplier IDs.
     * @return array Array of supplier data.
     */
    private function getSupplierList(array $suppliers)
    {
        if (empty($suppliers)) {
            return [];
        }
        $supplier_ids = array_map('intval', array_unique($suppliers));
        if (empty($supplier_ids)) {
            return [];
        }

        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_supplier_data');
        $sql->where('id_supplier IN (' . implode(',', $supplier_ids) . ')');

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$result) {
            return [];
        }

        return array_column($result, null, 'id_supplier');
    }

    /**
     * Get supplier data.
     *
     * @param array $suppliers Array of supplier IDs.
     * @return array Array of supplier data.
     */
    private function getSupplierData(array $suppliers)
    {
        if (empty($suppliers)) {
            return [];
        }
        $supplier_ids = array_map('intval', array_unique($suppliers));
        if (empty($supplier_ids)) {
            return [];
        }

        $sql = new DbQuery();
        $sql->select('s.id_supplier, s.name');
        $sql->from('supplier', 's');
        $sql->where('s.id_supplier IN (' . implode(',', $supplier_ids) . ')');

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$result) {
            return [];
        }

        return array_column($result, 'name', 'id_supplier');
    }

    /**
     * Get carrier data.
     *
     * @param array $carriers Array of carrier references.
     * @return array Array of carrier data.
     */
    private function getCarrierData(array $carriers)
    {
        if (empty($carriers)) {
            return [];
        }
        $carrier_references = array_map('intval', array_unique(array_filter($carriers)));
        if (empty($carrier_references)) {
            return [];
        }

        $sql = new DbQuery();
        $sql->select('c.id_reference, c.name');
        $sql->from('carrier', 'c');
        $sql->where('c.id_reference IN (' . implode(',', $carrier_references) . ') AND c.deleted = 0');

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$result) {
            return [];
        }

        return array_column($result, 'name', 'id_reference');
    }

    /**
     * Get invoicer data.
     *
     * @param array $invoicers Array of invoicer IDs.
     * @return array Array of invoicer data.
     */
    private function getInvoicerData(array $invoicers)
    {
        if (empty($invoicers)) {
            return [];
        }
        $invoicer_ids = array_map('intval', array_unique($invoicers));
        if (empty($invoicer_ids)) {
            return [];
        }

        $sql = new DbQuery();
        $sql->select('ems.id_seller, emsl.`shop_name`, CONCAT(emsl.`shop_name`, " - ", emsl.`shop_address`, " - ' . pSQL($this->l('UST-IDNR')) . ': ", ems.`vat_number`) as invoicer_data, emsl.notes');
        $sql->from('ets_mp_seller', 'ems');
        $sql->innerJoin('ets_mp_seller_lang', 'emsl', 'ems.id_seller = emsl.id_seller AND emsl.id_lang = ' . (int)$this->context->language->id);
        $sql->where('ems.id_seller IN (' . implode(',', $invoicer_ids) . ')');

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        if (!$result) {
            return [];
        }

        return array_column($result, null, 'id_seller');
    }

    /**
     * Get the cart data.
     *
     * @param Cart $cart The cart object.
     * @return array The cart data.
     */
    public function getThCartData(Cart $cart)
    {
        $th_products = (new CartPresenter())->present($cart)['products'];
        if (empty($th_products)) {
            return [];
        }

        // Define the desired order of totals
        $desired_order = ['products', 'discounts', 'shipping', 'payment'];

        // Get data from suppliers, carriers and invoicers
        $suppliers = array_column($th_products, 'id_supplier');
        $supplierData = $this->getSupplierList($suppliers);
        $suppliersData = $this->getSupplierData(array_unique(array_column($supplierData, 'id_supplier')));
        $carriersData = $this->getCarrierData(array_unique(array_column($supplierData, 'id_reference')));
        $invoicersData = $this->getInvoicerData(array_unique(array_column($supplierData, 'id_invoicer')));

        // Group products by invoicer
        $th_grouped_cart_by_invoicer = [];
        foreach ($th_products as $product_item) {
            $id_supplier_from_product = (int)($product_item['id_supplier'] ?? 0);
            $current_supplier_custom_data = null;
            $is_problematic = false;

            if ($id_supplier_from_product <= 0) {
                $is_problematic = true; // Condition: Product doesn't have a supplier
            } else {
                $current_supplier_custom_data = $supplierData[$id_supplier_from_product] ?? null;
                if ($current_supplier_custom_data === null) {
                    $is_problematic = true; // Condition: Supplier ID exists, but no custom data in th_supplier_data
                } else {
                    // Custom data exists. Now check if invoicer and carrier are properly set AND valid.
                    $id_invoicer_val = null;
                    $invoicer_properly_set_in_custom_data = isset($current_supplier_custom_data['id_invoicer']) && $current_supplier_custom_data['id_invoicer'] !== null && (int)$current_supplier_custom_data['id_invoicer'] > 0;
                    if ($invoicer_properly_set_in_custom_data) {
                        $id_invoicer_val = (int)$current_supplier_custom_data['id_invoicer'];
                    }

                    $id_carrier_ref_val = null;
                    $carrier_properly_set_in_custom_data = isset($current_supplier_custom_data['id_reference']) && $current_supplier_custom_data['id_reference'] !== null && (int)$current_supplier_custom_data['id_reference'] > 0;
                    if ($carrier_properly_set_in_custom_data) {
                        $id_carrier_ref_val = (int)$current_supplier_custom_data['id_reference'];
                    }

                    // Problem if invoicer is not set in custom data OR (it is set BUT NOT found in $invoicersData)
                    $problem_with_invoicer = !$invoicer_properly_set_in_custom_data || ($invoicer_properly_set_in_custom_data && !array_key_exists($id_invoicer_val, $invoicersData));

                    // Problem if carrier is not set in custom data OR (it is set BUT NOT found in $carriersData)
                    $problem_with_carrier = !$carrier_properly_set_in_custom_data || ($carrier_properly_set_in_custom_data && !array_key_exists($id_carrier_ref_val, $carriersData));

                    if ($problem_with_invoicer || $problem_with_carrier) {
                        $is_problematic = true;
                    }
                }
            }

            if ($is_problematic) {
                if (!isset($th_grouped_cart_by_invoicer[-1])) {
                    $th_grouped_cart_by_invoicer[-1] = [
                        'name' => $this->l('Unavailable right now'),
                        'products' => [],
                    ];
                }
                $th_grouped_cart_by_invoicer[-1]['products'][] = $product_item;
                continue; // Skip normal grouping for this problematic product
            }

            // If not problematic, proceed with normal grouping.
            // $current_supplier_custom_data is guaranteed non-null.
            // id_invoicer and id_reference from $current_supplier_custom_data are guaranteed to be set, positive, and exist as keys in $invoicersData and $carriersData.
            // $id_supplier_from_product is guaranteed > 0 and a key in $suppliersData.

            $id_invoicer = (int)$current_supplier_custom_data['id_invoicer'];
            $invoicer_name = $invoicersData[$id_invoicer]['invoicer_data']; // Direct access, key guaranteed
            $invoicer_name_short = $invoicersData[$id_invoicer]['shop_name']; // Direct access, key guaranteed

            if (!isset($th_grouped_cart_by_invoicer[$id_invoicer])) {
                $th_grouped_cart_by_invoicer[$id_invoicer] = [
                    'name' => $invoicer_name,
                    'name_short' => $invoicer_name_short,
                    'suppliers' => [],
                    'supplier_data' => $supplierData,
                    'carriers' => [],
                    'products' => [],
                    'pay_url' => '',
                    'notes' => $invoicersData[$id_invoicer]['notes'] ?? '',
                    'totals' => [
                        'shipping' => [
                            'label' => $this->l('Gesamtbetrag aller Versandkosten'),
                            'amount' => 0,
                            'value' => '',
                        ],
                        'payment' => [
                            'label' => $this->l('Gesamtbetrag inkl. aller Versandkosten'),
                            'amount' => 0,
                            'value' => '',
                        ],
                    ],
                ];
            }

            $supplier_name_for_grouping = $suppliersData[$id_supplier_from_product]; // Direct access, key guaranteed

            if (!isset($th_grouped_cart_by_invoicer[$id_invoicer]['suppliers'][$id_supplier_from_product])) {
                $th_grouped_cart_by_invoicer[$id_invoicer]['suppliers'][$id_supplier_from_product] = [
                    'name' => $supplier_name_for_grouping,
                    'products' => [],
                    'carrier' => $supplierData[$id_supplier_from_product]['id_reference'],
                    'supplier' => $id_supplier_from_product,
                    'notes' => $supplierData[$id_supplier_from_product]['notes'] ?? '',
                    'totals' => [
                        'shipping-name' => [
                            'label' => $this->l('Zusammenfassung - Versand durch'),
                            'value' => $carriersData[$supplierData[$id_supplier_from_product]['id_reference']] ?? '',
                        ],
                        'shipping' => [],
                        'sub-total' => [
                            'label' => $this->l('Gesamtbetrag Verlader: ' . $supplier_name_for_grouping),
                            'amount' => 0,
                            'value' => '',
                        ],
                    ],
                ];
            }
            $th_grouped_cart_by_invoicer[$id_invoicer]['suppliers'][$id_supplier_from_product]['products'][] = $product_item;

            $id_carrier_reference = (int)$current_supplier_custom_data['id_reference'];
            $carrier_name = $carriersData[$id_carrier_reference]; // Direct access, key guaranteed

            if (!isset($th_grouped_cart_by_invoicer[$id_invoicer]['carriers'][$id_carrier_reference])) {
                $th_grouped_cart_by_invoicer[$id_invoicer]['carriers'][$id_carrier_reference] = [
                    'name' => $carrier_name,
                    'products' => [],
                    'cost' => 0,
                ];
            }

            $th_grouped_cart_by_invoicer[$id_invoicer]['carriers'][$id_carrier_reference]['products'][] = $product_item;

            // Add product to invoicer's products
            $th_grouped_cart_by_invoicer[$id_invoicer]['products'][] = $product_item;
        }

        $this->processCartData($th_grouped_cart_by_invoicer, $cart);

        $this->addProductsToSeller($th_grouped_cart_by_invoicer);

        // Calculate totals and format them
        foreach ($th_grouped_cart_by_invoicer as $id_invoicer => &$invoicer_group) {
            if ($id_invoicer === -1) {
                continue;
            }
            // Calculate supplier totals
            foreach ($invoicer_group['suppliers'] as $key => $supplier_data) {
                $invoicer_group['suppliers'][$key]['totals']['sub-total']['amount'] = array_sum(array_column($supplier_data['products'], 'total_wt'));
            }

            // Format supplier totals
            if (isset($invoicer_group['suppliers']) && is_array($invoicer_group['suppliers'])) {
                foreach ($invoicer_group['suppliers'] as &$supplier_data) {
                    if (isset($supplier_data['totals']) && is_array($supplier_data['totals'])) {
                        foreach ($supplier_data['totals'] as &$total_data) {
                            if (isset($total_data['amount']) && is_numeric($total_data['amount'])) {
                                $total_data['value'] = $this->context->currentLocale->formatPrice(
                                    $total_data['amount'],
                                    $this->context->currency->iso_code
                                );
                            }
                        }
                        unset($total_data); // Unset reference
                    }
                }
                unset($supplier_data); // Unset reference
            }

            $invoicer_group['cart_key'] = $this->encodeNumber($invoicer_group['id_cart'], 'encode', 'xor');

            // Load cart discount codes
            $cart_obj = new Cart((int)$invoicer_group['id_cart']);
            $cart_presented = (new CartPresenter())->present($cart_obj);
            $cart_discount_codes = $cart_presented['vouchers']['added'];
            $invoicer_group['cart_discount_codes'] = array_map(function ($cart_discount_code) {
                return [
                    'id_cart_rule' => $this->encodeNumber($cart_discount_code['id_cart_rule'], 'encode', 'xor'),
                    'name' => $cart_discount_code['name'],
                    'reduction_formatted' => $cart_discount_code['reduction_formatted'],
                ];
            }, $cart_discount_codes);

            $invoicer_group['totals'] = array_merge(
                $invoicer_group['totals'],
                array_filter($cart_presented['subtotals'], function ($subtotal) {
                    return isset($subtotal['type']) && in_array($subtotal['type'], ['products', 'discount']);
                }),
            );

            // Calculate payment total
            // 1. Add products total
            $invoicer_group['totals']['payment']['amount'] = array_sum(array_column($invoicer_group['products'], 'total_wt'));
            // 2. Add shipping total
            $invoicer_group['totals']['payment']['amount'] += $invoicer_group['totals']['shipping']['amount'];
            // 3. Remove discount total
            if (isset($invoicer_group['totals']['discounts'])) {
                $invoicer_group['totals']['payment']['amount'] -= $invoicer_group['totals']['discounts']['amount'];
            }

            // Format invoicer totals
            if (isset($invoicer_group['totals']) && is_array($invoicer_group['totals'])) {
                foreach ($invoicer_group['totals'] as $key => &$total_data) {
                    if (!in_array($key, ['shipping', 'payment'])) {
                        continue;
                    }

                    if (isset($total_data['amount']) && is_numeric($total_data['amount'])) {
                        $total_data['value'] = $this->context->currentLocale->formatPrice(
                            $total_data['amount'],
                            $this->context->currency->iso_code
                        );
                    }
                }
                unset($total_data); // Unset reference
            }

            // Sort according to desired order
            $sorted_totals = [];
            foreach ($desired_order as $key) {
                if (isset($invoicer_group['totals'][$key])) {
                    $sorted_totals[$key] = $invoicer_group['totals'][$key];
                }
            }

            // Add any remaining totals that weren't in the desired order
            foreach ($invoicer_group['totals'] as $key => $total) {
                if (!isset($sorted_totals[$key])) {
                    $sorted_totals[$key] = $total;
                }
            }

            $invoicer_group['totals'] = $sorted_totals;
        }
        unset($invoicer_group); // Unset reference

        // Group problematic products before returning
        if (isset($th_grouped_cart_by_invoicer[-1])) {
            $problematic_products = $th_grouped_cart_by_invoicer[-1]['products'];

            // Initialize the -1 key with proper structure
            $th_grouped_cart_by_invoicer[-1] = [
                'name' => $this->l('Zurzeit nicht verfügbar'),
                'invoicers' => [],
                'products' => []
            ];

            // Group problematic products by invoicer first, then supplier
            foreach ($problematic_products as $product_item) {
                $id_supplier_from_product = (int)($product_item['id_supplier'] ?? 0);
                $current_supplier_custom_data = null;

                if ($id_supplier_from_product > 0 && isset($supplierData[$id_supplier_from_product])) {
                    $current_supplier_custom_data = $supplierData[$id_supplier_from_product];
                }

                // Determine invoicer grouping
                $invoicer_key = 'unknown';
                $invoicer_name = $this->l('Unknown');

                if ($current_supplier_custom_data && isset($current_supplier_custom_data['id_invoicer']) && $current_supplier_custom_data['id_invoicer'] > 0) {
                    $invoicer_id = (int)$current_supplier_custom_data['id_invoicer'];
                    if (isset($invoicersData[$invoicer_id])) {
                        $invoicer_key = 'invoicer_' . $invoicer_id;
                        $invoicer_name = $invoicersData[$invoicer_id]['invoicer_data'];
                    }
                }

                // Determine supplier grouping
                $supplier_key = 'unknown';
                $supplier_name = $this->l('Unknown');

                if ($id_supplier_from_product > 0 && isset($suppliersData[$id_supplier_from_product])) {
                    $supplier_key = 'supplier_' . $id_supplier_from_product;
                    $supplier_name = $suppliersData[$id_supplier_from_product];
                }

                // Create invoicer group within -1 if doesn't exist
                if (!isset($th_grouped_cart_by_invoicer[-1]['invoicers'][$invoicer_key])) {
                    $th_grouped_cart_by_invoicer[-1]['invoicers'][$invoicer_key] = [
                        'name' => $invoicer_name,
                        'suppliers' => [],
                        'products' => [],
                    ];
                }

                // Create supplier group within invoicer if doesn't exist
                if (!isset($th_grouped_cart_by_invoicer[-1]['invoicers'][$invoicer_key]['suppliers'][$supplier_key])) {
                    $th_grouped_cart_by_invoicer[-1]['invoicers'][$invoicer_key]['suppliers'][$supplier_key] = [
                        'name' => $supplier_name,
                        'products' => [],
                    ];
                }

                // Add product to all levels
                $th_grouped_cart_by_invoicer[-1]['invoicers'][$invoicer_key]['suppliers'][$supplier_key]['products'][] = $product_item;
            }
        }

        // Remove unncesesary data for template
        $keys_to_remove = ['supplier_data', 'carriers', 'products', 'id_cart'];
        foreach ($th_grouped_cart_by_invoicer as $id_invoicer => &$invoicer_data) {
            foreach ($keys_to_remove as $key) {
                unset($invoicer_data[$key]);
            }
        }
        unset($invoicer_data);

        return $th_grouped_cart_by_invoicer;
    }

    /**
     * Assign new cart data to the context.
     *
     * @param Cart $cart The cart object.
     *
     * @return void
     */
    public function assignNewCartData(Cart $cart)
    {
        $cart_data = $this->getThCartData($cart);
        $this->context->smarty->assign([
            'th_cart' => $cart_data,
            'th_cart_count' => count(array_filter(array_keys($cart_data), function ($item) {
                return $item !== -1;
            })),
        ]);
    }

    /**
     * Integrate with marketplace module to assign products to sellers
     *
     * Automatically assigns cart products to their respective marketplace sellers when
     * the ets_marketplace module is enabled. Ensures sellers have access to sold products.
     *
     * @param array $th_cart_data Cart data with invoicer/seller information
     */
    private function addProductsToSeller(array $th_cart_data)
    {
        // Add informations on mk module
        if (Module::isEnabled('ets_marketplace')) {
            // Get instance only if class Ets_mp_seller is not loaded
            if (!class_exists('Ets_mp_seller')) {
                Module::getInstanceByName('ets_marketplace');
            }

            // Get invoicer id from order
            foreach ($th_cart_data as $id_invoicer => $invoicer_data) {
                if ($id_invoicer === -1) {
                    continue;
                }

                $seller = new Ets_mp_seller($id_invoicer);
                if (Validate::isLoadedObject($seller)) {
                    // Add all products from order to seller
                    foreach ($invoicer_data['products'] as $product) {
                        // Check if product is already assigned to this seller
                        if (!$seller->checkHasProduct($product['id_product'])) {
                            // Add product to seller (approved=1, active=1)
                            $seller->addProduct($product['id_product'], 1, 1);
                        }
                    }
                }
            }
        }
    }

    /**
     * Process cart data and manage child cart creation/synchronization
     *
     * Handles the main cart splitting logic: creates new child carts or syncs existing ones,
     * adds payment URLs, and calculates shipping costs for each invoicer.
     *
     * @param array $th_cart_data Cart data array (passed by reference)
     * @param Cart $parent_cart The parent cart to process
     */
    private function processCartData(array &$th_cart_data, Cart $parent_cart)
    {
        $child_carts = $this->getChildCarts($parent_cart->id);

        foreach ($th_cart_data as $id_invoicer => &$invoicer_data) {
            if ($id_invoicer === -1) {
                continue;
            }

            if (isset($child_carts[$id_invoicer])) {
                $this->syncChildCart($th_cart_data, $id_invoicer, (int)$child_carts[$id_invoicer]['id_cart_child'], $parent_cart);
                $this->addPayUrlToInvoicer($th_cart_data, $id_invoicer, $child_carts[$id_invoicer]['key']);
                $this->addShippingCostsToSupplier($invoicer_data);
                $this->checkDiscounts($invoicer_data);

                continue;
            }

            $this->createChildCart($th_cart_data, $id_invoicer, $parent_cart);
            $this->addPayUrlToInvoicer($th_cart_data, $id_invoicer, $invoicer_data['key'] ?? '');
            $this->addShippingCostsToSupplier($invoicer_data);
            $this->checkDiscounts($invoicer_data);
        }
        unset($invoicer_data);
    }

    private function checkDiscounts(array &$invoicer_data)
    {

    }

    /**
     * Add pay URL to invoicer.
     *
     * @param array $th_cart_data The cart data.
     * @param int $id_invoicer The invoicer ID.
     * @param string $key The key.
     *
     * @return void
     */
    private function addPayUrlToInvoicer(array &$th_cart_data, int $id_invoicer, string $key)
    {
        if (!isset($th_cart_data[$id_invoicer]) || empty($key)) {
            return;
        }

        $th_cart_data[$id_invoicer]['pay_url'] = $this->context->link->getPageLink('order', null, $this->context->language->id, ['key' => $key]);
    }

    /**
     * Calculate and add shipping costs to invoicer and supplier data
     *
     * Calculates shipping costs for the cart and distributes them to the appropriate suppliers.
     * Updates totals for both invoicer-level and supplier-level shipping costs.
     *
     * @param array $invoicer_data Invoicer data array (passed by reference)
     */
    private function addShippingCostsToSupplier(array &$invoicer_data)
    {
        if (!isset($invoicer_data['id_cart']) || !isset($invoicer_data['suppliers'])) {
            return;
        }

        // Get shipping costs for the cart
        $shipping_costs = $this->getShippingCost(new Cart($invoicer_data['id_cart']));

        //spamgol
        $shipping_costs_data = $shipping_costs['data'];

        foreach ($shipping_costs_data as &$shipping_cost_data) {
            unset($shipping_cost_data['products']);
            $carrier = new Carrier($shipping_cost_data['id_carrier']);
            $shipping_cost_data['delay'] = $carrier->delay[$this->context->language->id];
        }

        Db::getInstance()->update('th_s_cart', [
            'shipping_extracontent' => json_encode($shipping_costs_data),
        ], 'id_cart_child = ' . (int)$invoicer_data['id_cart']);

        unset($shipping_costs_data);

        // Add shipping costs to the invoicer
        $invoicer_data['totals']['shipping']['amount'] = $shipping_costs['total_price_with_tax'];

        // Add shipping costs to the suppliers
        $suppliers_reverse = array_reverse($invoicer_data['suppliers']);
        foreach ($suppliers_reverse as &$supplier) {
            if (!isset($shipping_costs['data'][$supplier['carrier']])) {
                continue;
            }

            $invoicer_data['suppliers'][$supplier['supplier']]['totals']['shipping'] = [
                'label' => $this->l('Versandkosten'),
                'amount' => $shipping_costs['data'][$supplier['carrier']]['cost'],
                'value' => '',
            ];

            unset($shipping_costs['data'][$supplier['carrier']]);
        }
        unset($supplier);

        $cart = new Cart($invoicer_data['id_cart']);
        $cart_rules = $cart->getCartRules();

        foreach ($cart_rules as $rule) {
            if (!empty($rule['free_shipping'])) {
                // Set all carrier costs to 0
                foreach ($invoicer_data['carriers'] as &$carrier) {
                    $carrier['cost'] = 0;
                }
                unset($carrier);

                // Set the shipping total to 0
                if (isset($invoicer_data['totals']['shipping'])) {
                    $invoicer_data['totals']['shipping']['amount'] = 0;
                }
                break;
            }
        }
    }

    /**
     * Get the child carts.
     *
     * @param int $id_cart The cart ID.
     * @return array The child carts.
     */
    private function getChildCarts(int $id_cart)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('id_cart_context = ' . (int)$id_cart);
        $sql->where('paid = 0');

        return array_column(Db::getInstance()->executeS($sql), null, 'id_invoicer');
    }

    /**
     * Validate carrier availability for invoicer's products
     *
     * Checks if each product's assigned carrier can deliver to the cart address.
     * Moves products with unavailable carriers to the "unavailable" group (invoicer -1).
     *
     * @param array $th_cart_data Cart data array (passed by reference)
     * @param int $id_invoicer The invoicer ID to check products for
     * @param Cart $child_cart The child cart for delivery address validation
     */
    private function checkProductsCarrier(array &$th_cart_data, int $id_invoicer, Cart $child_cart)
    {
        $id_carrier_thshipping = (int)Carrier::getCarrierByReference(Configuration::get('THSHIPPING_CARRIER_ID'))->id;

        // Check if all products have a available carrier
        foreach ($th_cart_data[$id_invoicer]['products'] ?? [] as $key => $product) {
            $product = $product->jsonSerialize();

            // Get available carriers for the product
            $carrier_list = Carrier::getAvailableCarrierList(
                new Product($product['id_product']),
                null,
                $child_cart->id_address_delivery,
                null,
                $child_cart
            );
            unset($carrier_list[$id_carrier_thshipping]);

            $product_id_supplier = $product['id_supplier'];
            $supplier_carrier = $th_cart_data[$id_invoicer]['suppliers'][$product_id_supplier]['carrier'];

            if (!in_array(Carrier::getCarrierByReference($supplier_carrier)->id, $carrier_list)) {
                // Remove from current invoicer's products
                unset($th_cart_data[$id_invoicer]['products'][$key]);

                // Remove from supplier's products
                foreach ($th_cart_data[$id_invoicer]['suppliers'][$product_id_supplier]['products'] as $supplier_key => $supplier_product) {
                    if (
                        $supplier_product['id_product'] == $product['id_product'] &&
                        $supplier_product['id_product_attribute'] == $product['id_product_attribute']
                    ) {
                        unset($th_cart_data[$id_invoicer]['suppliers'][$product_id_supplier]['products'][$supplier_key]);
                        break;
                    }
                }

                // Check if supplier has products
                if (empty($th_cart_data[$id_invoicer]['suppliers'][$product_id_supplier]['products'])) {
                    unset($th_cart_data[$id_invoicer]['suppliers'][$product_id_supplier]);
                }

                // Remove from carrier's products
                foreach ($th_cart_data[$id_invoicer]['carriers'][$supplier_carrier]['products'] as $carrier_key => $carrier_product) {
                    if (
                        $carrier_product['id_product'] == $product['id_product'] &&
                        $carrier_product['id_product_attribute'] == $product['id_product_attribute']
                    ) {
                        unset($th_cart_data[$id_invoicer]['carriers'][$supplier_carrier]['products'][$carrier_key]);
                        break;
                    }
                }

                // Add to invoicer -1 (unavailable products)
                if (!isset($th_cart_data[-1])) {
                    $th_cart_data[-1] = [
                        'name' => $this->l('Unavailable right now'),
                        'products' => [],
                    ];
                }
                $th_cart_data[-1]['products'][] = $product;
            }
        }

        // Check if invoicer has products
        if (empty($th_cart_data[$id_invoicer]['products'])) {
            unset($th_cart_data[$id_invoicer]);
        }
    }

    /**
     * Synchronize existing child cart with parent cart changes
     *
     * Updates child cart products/quantities to match parent cart, validates carrier availability,
     * and ensures cart details (address, currency, etc.) are synchronized with parent.
     *
     * @param array $th_cart_data Cart data array (passed by reference)
     * @param int $id_invoicer The invoicer ID
     * @param int $id_cart_child The existing child cart ID to sync
     * @param Cart $parent_cart The parent cart to sync from
     */
    private function syncChildCart(array &$th_cart_data, int $id_invoicer, int $id_cart_child, Cart $parent_cart)
    {
        $child_cart = new Cart($id_cart_child);
        $current_cart_products = $child_cart->getProducts();

        // Sync cart details from main cart to child cart
        $fields_to_sync = [
            'id_shop',
            'id_shop_group',
            'id_lang',
            'id_address_delivery',
            'id_address_invoice',
            'id_currency',
            'id_customer',
            'id_guest',
            'secure_key',
            'recyclable',
            'gift',
            'gift_message',
        ];

        // Sync each field if different
        foreach ($fields_to_sync as $field) {
            if ($child_cart->$field != $parent_cart->$field) {
                $child_cart->$field = $parent_cart->$field;
            }
        }

        // Save the child cart with updated details
        $child_cart->save();

        $this->checkProductsCarrier($th_cart_data, $id_invoicer, $child_cart);

        // Create a map of products that should exist in the cart
        $expected_products = [];
        foreach ($th_cart_data[$id_invoicer]['products'] ?? [] as &$product) {
            $key = $product['id_product'] . '_' . ($product['id_product_attribute'] ?? 0);
            $expected_products[$key] = [
                'id_product' => (int)$product['id_product'],
                'id_product_attribute' => (int)($product['id_product_attribute'] ?? 0),
                'quantity' => (int)$product['quantity']
            ];
        }
        unset($product);

        // Create a map of current cart products for easy lookup
        $current_products = [];
        foreach ($current_cart_products as $cart_product) {
            $key = $cart_product['id_product'] . '_' . $cart_product['id_product_attribute'];
            $current_products[$key] = (int)$cart_product['quantity'];
        }

        // Check current cart products and remove those that shouldn't be there
        foreach ($current_cart_products as $cart_product) {
            $key = $cart_product['id_product'] . '_' . $cart_product['id_product_attribute'];

            if (!isset($expected_products[$key])) {
                // This product shouldn't be in the cart, remove it
                $child_cart->deleteProduct(
                    (int)$cart_product['id_product'],
                    (int)$cart_product['id_product_attribute']
                );
            }
        }

        // Ensure all expected products are in the cart with correct quantities
        foreach ($expected_products as $key => $expected_product) {
            $current_qty = $current_products[$key] ?? 0;
            $expected_qty = $expected_product['quantity'];

            if ($current_qty != $expected_qty) {
                if ($current_qty == 0) {
                    // Product doesn't exist in cart, add it
                    $child_cart->updateQty(
                        $expected_qty,
                        $expected_product['id_product'],
                        $expected_product['id_product_attribute']
                    );
                } else {
                    // Product exists, calculate difference
                    $qty_difference = $expected_qty - $current_qty;

                    if ($qty_difference > 0) {
                        // Need to increase quantity
                        $child_cart->updateQty(
                            $qty_difference,
                            $expected_product['id_product'],
                            $expected_product['id_product_attribute']
                        );
                    } else {
                        // Need to decrease quantity
                        $child_cart->updateQty(
                            abs($qty_difference),
                            $expected_product['id_product'],
                            $expected_product['id_product_attribute'],
                            false,
                            'down'
                        );
                    }
                }
            }
        }

        // Update shipping data
        $shipping_data = $this->constructShippingData($th_cart_data[$id_invoicer] ?? ['carriers' => []]);
        Db::getInstance()->update('th_s_cart', [
            'shipping' => $shipping_data,
        ], 'id_cart_child = ' . (int)$child_cart->id);

        // Add child cart ID to invoicer data only if invoicer exists
        if (isset($th_cart_data[$id_invoicer])) {
            $th_cart_data[$id_invoicer]['id_cart'] = $child_cart->id;

            // Sync discount rules to the synchronized child cart
            if (QuantityDiscountIntegration::isQuantityDiscountProActive()) {
                QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($parent_cart->id);
            }
        }
    }

    /**
     * Create a new child cart for split-order functionality
     *
     * Clones the parent cart, validates carrier availability, and creates a new child cart
     * for the specific invoicer with only their products. Generates a payment key for checkout.
     *
     * @param array $th_cart_data Cart data array (passed by reference)
     * @param int $id_invoicer The invoicer ID to create cart for
     * @param Cart $parent_cart The parent cart to clone from
     */
    private function createChildCart(array &$th_cart_data, int $id_invoicer, Cart $parent_cart)
    {
        // Clone context cart
        $context_cart = $parent_cart;
        $child_cart = clone $context_cart;
        $child_cart->id = null;
        $child_cart->save();

        $this->checkProductsCarrier($th_cart_data, $id_invoicer, $child_cart);

        $payment_key = Tools::passwdGen(16);

        // Add child cart to th_s_cart
        Db::getInstance()->insert('th_s_cart', [
            'id_cart_context' => $context_cart->id,
            'id_cart_child' => $child_cart->id,
            'id_invoicer' => $id_invoicer,
            'key' => $payment_key,
            'shipping' => $this->constructShippingData($th_cart_data[$id_invoicer] ?? ['carriers' => []]),
        ]);

        // Add products to child cart only if invoicer exists
        if (isset($th_cart_data[$id_invoicer])) {
            // Add products to child cart
            foreach ($th_cart_data[$id_invoicer]['products'] as $product) {
                $child_cart->updateQty($product['quantity'], $product['id_product'], $product['id_product_attribute'], $product['id_customization']);
            }

            $th_cart_data[$id_invoicer]['id_cart'] = $child_cart->id;
            $th_cart_data[$id_invoicer]['key'] = $payment_key;

            // Sync discount rules to the new child cart
            if (QuantityDiscountIntegration::isQuantityDiscountProActive()) {
                QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($context_cart->id);
            }
        }
    }

    /**
     * Construct shipping data.
     *
     * @param array $invoicer_data The invoicer data.
     * @return string The shipping data.
     */
    private function constructShippingData(array $invoicer_data)
    {
        $shipping_data = [];

        foreach ($invoicer_data['carriers'] ?? [] as $id_carrier => $carrier_data) {
            // Prepare product list
            $product_list = [];
            foreach ($carrier_data['products'] as $product) {
                $product_list[$product['id_product'] . '_' . $product['id_product_attribute']] = $product['quantity'];
            }

            // Add carrier data to shipping data
            $shipping_data[$id_carrier] = [
                'id_carrier' => $id_carrier,
                'products' => $product_list,
            ];
        }

        return json_encode($shipping_data);
    }

    /**
     * Get cart by key.
     *
     * @param string $key The key.
     * @return array The cart data.
     */
    public function getCartByKey(string $key)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('`key` = "' . pSQL($key) . '"');

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Get cart by child cart.
     *
     * @param int $id_cart_child The child cart ID.
     * @return array The cart data.
     */
    public function getCartByChildCart(int $id_cart_child = -1)
    {
        if($id_cart_child == -1){
            return;
        }

        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('id_cart_child = ' . (int)$id_cart_child);

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Get cart by parent cart.
     *
     * @param int $id_cart_parent The parent cart ID.
     * @return array The cart data.
     */
    public function getCartByParentCart(int $id_cart_parent)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('th_s_cart');
        $sql->where('id_cart_context = ' . (int)$id_cart_parent);

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Check if all cart products can be shipped to the delivery address
     *
     * Validates that each product's assigned carrier can deliver to the cart's address.
     * Returns products that have carrier conflicts for the delivery address.
     *
     * @param Cart $cart The cart to validate
     * @return array ['success' => bool, 'products' => array] - problematic products list
     */
    public function checkAddress(Cart $cart)
    {
        $return = [
            'success' => false,
            'products' => [],
        ];

        // Load cart products & get supplier data
        $products = $cart->getProducts();
        $supplierData = $this->getSupplierList(array_column($products, 'id_supplier'));
        foreach ($supplierData as &$supplier) {
            $supplier['id_carrier'] = Carrier::getCarrierByReference((int)$supplier['id_reference'])->id;
        }
        unset($supplier);

        // Check if all products have a available carrier
        foreach ($products as $product) {
            // Get available carriers for the product
            $carrier_list = Carrier::getAvailableCarrierList(
                new Product($product['id_product']),
                null,
                $cart->id_address_delivery,
                null,
                $cart
            );
            unset($carrier_list[(int)Carrier::getCarrierByReference(Configuration::get('THSHIPPING_CARRIER_ID'))->id]);

            if (!in_array($supplierData[$product['id_supplier']]['id_carrier'], $carrier_list)) {
                $return['products'][] = [
                    'id' => $product['id_product'] . '_' . $product['id_product_attribute'],
                    'name' => $product['name'],
                ];
            }
        }

        if (empty($return['products'])) {
            $return['success'] = true;
        }

        return $return;
    }

    public function getOrderShippingCost($params, $shipping_cost)
    {
        return $shipping_cost;
    }

    public function getOrderShippingCostExternal($params)
    {
        return true;
    }

    /**
     * Apply discount code.
     *
     * @return void
     */
    public function applyDiscountCode()
    {
        $discount_code = pSQL(Tools::getValue('discountCode'));
        $id_cart_rule = CartRule::getIdByCode($discount_code);
        $id_cart = $this->encodeNumber(pSQL(Tools::getValue('cartKey')), 'decode', 'xor');

        // First check if this is a quantitydiscountpro voucher
        if (QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            $parent_cart_id = QuantityDiscountIntegration::getParentCartId($id_cart);
            if ($parent_cart_id) {
                // This is a child cart, handle through parent cart
                $result = QuantityDiscountIntegration::handleVoucherAddition($discount_code, $parent_cart_id);
                if ($result) {
                    die(json_encode([
                        'success' => true,
                    ]));
                }
            } else {
                // This is a parent cart, check if it has child carts
                if (QuantityDiscountIntegration::hasChildCarts($id_cart)) {
                    $result = QuantityDiscountIntegration::handleVoucherAddition($discount_code, $id_cart);
                    if ($result) {
                        die(json_encode([
                            'success' => true,
                        ]));
                    }
                }
            }
        }

        if (!$id_cart_rule) {
            die(json_encode([
                'success' => false,
                'message' => $this->l('Ungültiger Rabattcode'),
            ]));
        }

        // Clone context and set cart
        $cart_rule = new CartRule($id_cart_rule);
        $context = Context::getContext()->cloneContext();
        $context->cart = new Cart((int)$id_cart);

        // Check if the cart rule is valid
        $error = $cart_rule->checkValidity($context);
        if ($error) {
            die(json_encode([
                'success' => false,
                'message' => $error,
            ]));
        }

        // Add cart rule to cart
        $context->cart->addCartRule($cart_rule->id);

        die(json_encode([
            'success' => true,
        ]));
    }

    public function removeDiscountCode()
    {
        $raw_id_discount = pSQL(Tools::getValue('idDiscount'));
        $raw_id_cart = pSQL(Tools::getValue('cartKey'));

        // Check if the discount ID is already encoded (base64) or raw (numeric)
        if (is_numeric($raw_id_discount)) {
            // It's a raw numeric value (from checkout URL)
            $id_discount = (int)$raw_id_discount;
        } else {
            // It's encoded (from cart.js data attributes)
            $id_discount = $this->encodeNumber($raw_id_discount, 'decode', 'xor');
        }

        // Check if the cart key is already encoded or raw
        if (is_numeric($raw_id_cart)) {
            // It's a raw numeric value
            $id_cart = (int)$raw_id_cart;
        } else {
            // It's encoded
            $id_cart = $this->encodeNumber($raw_id_cart, 'decode', 'xor');
        }

        $cart = new Cart($id_cart);
        $cart->removeCartRule($id_discount);

        // Handle quantitydiscountpro integration - remove from parent cart too
        if (QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            $parent_cart_id = QuantityDiscountIntegration::getParentCartId($id_cart);
            if ($parent_cart_id) {
                // This is a child cart, remove from parent cart and all other child carts
                QuantityDiscountIntegration::handleVoucherRemoval($id_discount, $parent_cart_id);
            }
        }

        die(json_encode([
            'success' => true,
        ]));
    }

    /**
     * Encode/Decode number with different operations for secure data transmission
     *
     * This function provides multiple encoding/decoding methods to obfuscate numbers
     * before sending them to the frontend and decode them back when received.
     * Useful for hiding sensitive IDs like cart IDs, user IDs, etc.
     *
     * Available methods:
     * - 'base64': Simple base64 encoding (good for URLs)
     * - 'xor': XOR with fixed key + base64 (more secure)
     * - 'reverse': Reverse number digits (simple obfuscation)
     * - 'offset': Add/subtract fixed offset + base64 (medium security)
     * - 'multiply': Multiply/divide by factor + base64 (medium security)
     *
     * @param mixed $number The number to encode/decode
     * @param string $action The action: 'encode' to obfuscate, 'decode' to restore original
     * @param string $method The encoding method (default: 'base64')
     * @return mixed The processed number (string when encoded, int when decoded)
     *
     * @example
     * $encoded = $this->encodeNumber(12345, 'encode', 'xor');
     * $original = $this->encodeNumber($encoded, 'decode', 'xor'); // Returns 12345
     */
    public function encodeNumber($number, $action, $method = 'base64')
    {
        switch ($method) {
            case 'base64':
                if ($action === 'encode') {
                    return base64_encode($number);
                } else {
                    return base64_decode($number);
                }

            case 'xor':
                // XOR with a fixed key (XOR is reversible)
                $key = 5461885093797834;
                if ($action === 'encode') {
                    $xor_result = (int)$number ^ $key;
                    return base64_encode($xor_result);
                } else {
                    $decoded = base64_decode($number);
                    return (int)$decoded ^ $key;
                }

            case 'reverse':
                // Reverse the number digits
                if ($action === 'encode') {
                    return strrev($number);
                } else {
                    return strrev($number);
                }

            case 'offset':
                // Add/subtract offset
                $offset = 324317903927075;
                if ($action === 'encode') {
                    return base64_encode($number + $offset);
                } else {
                    return base64_decode($number) - $offset;
                }

            case 'multiply':
                // Multiply/divide by factor
                $factor = 7;
                if ($action === 'encode') {
                    return base64_encode($number * $factor);
                } else {
                    return base64_decode($number) / $factor;
                }

            default:
                return $number;
        }
    }

    /**
     * Add carrier.
     *
     * @return Carrier The carrier.
     */
    private function addCarrier()
    {
        $name = Configuration::get('THSHIPPING_CARRIER_NAME');
        $delivery_time = Configuration::get('THSHIPPING_CARRIER_DELIVERY_TIME');

        $carrier = new Carrier();
        $carrier->name = empty($name) ? 'Shipping' : $name;
        $carrier->is_module = true;
        $carrier->active = 1;
        $carrier->need_range = 1;
        $carrier->shipping_external = 0;
        $carrier->shipping_handling = 0;
        $carrier->range_behavior = 0;
        $carrier->external_module_name = $this->name;
        $carrier->shipping_method = 1;
        $carrier->max_weight = 0;
        $carrier->position = 999;

        foreach (Language::getLanguages() as $lang) {
            $carrier->delay[$lang['id_lang']] = empty($delivery_time) ? 'ca. 3-5 working days' : $delivery_time;
        }

        if ($carrier->save()) {
            $carrier->setTaxRulesGroup($this->id_tax_rules_group, true);

            $this->addZones($carrier);
            $this->addGroups($carrier);
            $this->addRanges($carrier);

            // Delete old carrier
            if ($id_carrier_old = Configuration::get('THSHIPPING_CARRIER_ID')) {
                $carrier_old = Carrier::getCarrierByReference($id_carrier_old);
                $carrier_old->deleted = true;
                $carrier_old->update();
            }

            // Update carrier ID
            Configuration::updateValue('THSHIPPING_CARRIER_ID', (new Carrier($carrier->id))->id_reference);

            return $carrier;
        }

        return false;
    }

    /**
     * Add groups.
     *
     * @param Carrier $carrier The carrier.
     */
    private function addGroups($carrier): void
    {
        $groups_ids = array();
        $groups = Group::getGroups(Context::getContext()->language->id);
        foreach ($groups as $group)
            $groups_ids[] = $group['id_group'];

        $carrier->setGroups($groups_ids);
    }

    /**
     * Add ranges.
     *
     * @param Carrier $carrier The carrier.
     */
    private function addRanges($carrier): void
    {
        $range_price = new RangePrice();
        $range_price->id_carrier = $carrier->id;
        $range_price->delimiter1 = '0';
        $range_price->delimiter2 = '10000';
        $range_price->add();

        $range_weight = new RangeWeight();
        $range_weight->id_carrier = $carrier->id;
        $range_weight->delimiter1 = '0';
        $range_weight->delimiter2 = '10000';
        $range_weight->add();
    }

    /**
     * Add zones.
     *
     * @param Carrier $carrier The carrier.
     */
    private function addZones($carrier): void
    {
        $zones = Zone::getZones();

        foreach ($zones as $zone) {
            $carrier->addZone($zone['id_zone']);
        }
    }

    /**
     * Hook to display additional carrier content in checkout.
     *
     * @param array $params Hook parameters containing carrier information.
     * @return string HTML content to be displayed.
     */
    public function hookDisplayCarrierExtraContent($params)
    {
        $sql = new DbQuery();
        $sql->select('shipping_extracontent');
        $sql->from('th_s_cart');
        $sql->where('id_cart_child = ' . (int)$params['cart']->id);

        $shipping_extracontent = json_decode(Db::getInstance()->getValue($sql), true);

        if (empty($shipping_extracontent)) {
            return '';
        }

        $cart = new Cart((int)$params['cart']->id);
        $cart_rules = $cart->getCartRules();

        $th_display_extracontent = true;
        foreach ($cart_rules as $rule) {
            if (!empty($rule['free_shipping'])) {
                $th_display_extracontent = false;
            }
        }

        $thcarrier_extra_content = Configuration::get('THSHIPPING_CARRIER_EXTRA_CONTENT');
//        if (isset($_SERVER) && isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] == '**************') {
//            echo"<pre>";var_dump( $th_free_shipping);
//            exit;
//        }

        $this->context->smarty->assign([
            'shipping_extracontent' => $shipping_extracontent,
            'th_display_extracontent' => $th_display_extracontent,
            'thcarrier_extra_content' => $thcarrier_extra_content,
        ]);

        return $this->display(__FILE__, 'views/templates/hook/carrier_extra_content.tpl');
    }

    /**
     * Hook called when cart is saved - sync discount rules to child carts
     */
    public function hookActionCartSave($params)
    {
        if (!QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            return;
        }

        $cart = $params['cart'];

        // If this is a parent cart with child carts, sync discount rules
        if (QuantityDiscountIntegration::hasChildCarts($cart->id)) {
            QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($cart->id);
        }
    }

    /**
     * Hook called when cart rule is added - handle for child carts
     */
    public function hookActionCartRuleAfterAdd($params)
    {
        if (!QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            return;
        }

        $cart = $params['cart'];
        $cartRule = $params['cart_rule'];

        // If this is a parent cart with child carts, handle discount rules
        if (QuantityDiscountIntegration::hasChildCarts($cart->id)) {
            // Check if this is a quantitydiscountpro rule
            if (isset($cartRule->code) && $cartRule->code) {
                QuantityDiscountIntegration::handleVoucherAddition($cartRule->code, $cart->id);
            } else {
                // For other cart rules, just sync them to child carts
                QuantityDiscountIntegration::applyParentDiscountRulesToChildCarts($cart->id);
            }
        }
    }

    /**
     * Hook called when cart rule is removed - handle for child carts
     */
    public function hookActionCartRuleAfterRemove($params)
    {
        if (!QuantityDiscountIntegration::isQuantityDiscountProActive()) {
            return;
        }

        $cart = $params['cart'];
        $cartRule = $params['cart_rule'];

        // If this is a parent cart with child carts, handle discount rules
        if (QuantityDiscountIntegration::hasChildCarts($cart->id)) {
            QuantityDiscountIntegration::handleVoucherRemoval($cartRule->id, $cart->id);
        }
    }

    public function hookActionCarrierProcess($params)
    {
        if (empty($params['cart'])) {
            return;
        }

        $cart = $params['cart'];

        $shipping_costs = $this->getShippingCost($cart);

        $shipping_costs_data = $shipping_costs['data'];

        foreach ($shipping_costs_data as &$shipping_cost_data) {
            unset($shipping_cost_data['products']);
            $carrier = new Carrier($shipping_cost_data['id_carrier']);
            $shipping_cost_data['delay'] = $carrier->delay[$this->context->language->id];
        }

        Db::getInstance()->update('th_s_cart', [
            'shipping_extracontent' => json_encode($shipping_costs_data),
        ], 'id_cart_child = ' . (int)$cart->id);

    }
}
